
local ir_chip = require "hlc668"
local e_chip = require "hlw8110"
local ntc = require "ntc"
local key = require "key"
 ac_status = {
    ['power_status']            = 0,               --电源状态
    ["runningstatus"]           = 0,              --运行状态-不存
    ['compress_status']         = 0,            --压缩机状态-不存
    ["mode"]                    = 1,                 --模式      -重启保存/
    ["temp"]                    = 26,                --设定温度  -重启保存/
    ["fan"]                     = 0,                 --风速      -重启保存/
    ["fan_type"]                = 1,                 --扫风      -重启保存/
    ["temp_box"]                = 0,                --内部温度  -不存
    ["temp_indoor"]             = 0,                --外部温度  -不存
    ["humi"]                    = 0,                 --湿度，没用 -不存
    ['code']                    = 0,                 --码库号    -改变保存/
    ['factor']                  = 1,                  --功率系数  -改变保存/
    ['i']                       = 0,                 --电流
    ['u']                       = 0,                 --电压
    ['p']                       = 0,                 --功率
    ['p_f']                     = 0,                 --功率因数
    ['ep']                      = 0,                 --电量                  -积累保存/
	['ep_init']	                = 0,					--初始化电量            -积累保存/
    ['compressor_time']         = 0,            --压缩机总运行时间      -长定时保存/
    ['current_running_time']    = 0,            --压缩机本次运行的时间  -不保存
    ['using_time']              = 0,                 --空调总使用时间        -长定时保存/
    ['run_power']               = 10,                 --开机功耗              -改变保存/
    ['c_run_power']             = 60,                --压缩机运行功耗       -改变保存/
    ['rssi']                    = 0,                       --信号值
    ['CPU_TEMP']                = 0,                    --CPU温度
    ['temp_offset']              = 0                --温度偏移量
}

--获取状态
function get_relay_status()
    return 1
end

--设置开关
function relay_switch(value)
    log.info("relay_switch", value)
end

--设置开关功率阈值
function set_run_power(value)
    ac_status.run_power = value
    sys.publish("SAVE_ONE_DATA", 'run_power',value)
    log.info("set run power", value)
    return true
end

--设置压缩机开关功率阈值
function set_c_run_power(value)
    ac_status.c_run_power = value
    sys.publish("SAVE_ONE_DATA", 'c_run_power',value)
    log.info("set c run power", value)
    return true
end



 function reset_params()

    ac_status.ep_init         = 0
    ac_status.ep              = 0
    ac_status.compressor_time = 0
    ac_status.using_time      = 0
    ac_status.run_power       = 10
    ac_status.c_run_power     = 60

    sys.publish("SAVE_ONE_DATA", 'run_power',10)
    sys.publish("SAVE_ONE_DATA", 'c_run_power',60)


    --其它参数安全重启的时候会保存

    log.info("reset_params", "store ac_data")
    sys.publish("RESET_PARAMS_DONE")
end

sys.subscribe("RESET_PARAMS", reset_params)
-- 命令代号
-- 1 继电器
-- 2 开关机
-- 3 制冷开机
-- 4 制热开机
-- 5 模式
-- 6 温度设定
-- 7 风速
-- 8 自动模式开机
-- 9 除湿开机
-- 10 一键匹配
-- 11 设置码库
-- 12 送风开机
-- 13 发送通用命令
-- 14 设置电能、功率比例因数
-- 15 设置开机功率
-- 16 设置压缩机运行功率
-- 17 获取芯片版本号
-- 18 恢复出厂设置
local cmd_table = {
    relay_switch,
    bsp_ir_chip_set_air_cond_power,
    bsp_ir_chip_set_air_cond_power_on_cool,
    bsp_ir_chip_set_air_cond_power_on_heat,
    bsp_ir_chip_set_air_cond_mode,
    bsp_ir_chip_set_air_cond_temp,
    bsp_ir_chip_set_air_cond_fan,
    bsp_ir_chip_set_air_cond_power_on_auto,
    bsp_ir_chip_set_air_cond_power_on_humi,
    bsp_ir_chip_one_step_match,
    bsp_ir_chip_set_code,
    bsp_ir_chip_set_air_cond_power_on_fan,
    bsp_ir_chip_send_common_command,
    set_power_factor,
    set_run_power,
    set_c_run_power,
    bsp_ir_get_chip_version,
    reset_params,
}


--处理设备命令
function air_condition_proc_cmd(addr, value)
    local ret = -1
    log.info("air_condition_proc_cmd", addr, value)
    if cmd_table[addr] ~= nil then
        for i=1,1 do
            ret = cmd_table[addr](value)
            if ret == true then
                break
            end
        end
    end

    if ret == true then
        ret = 1
    else
        ret = -1
    end

    return ret
end

--继电器初始化
function relay_init()
    --set_relay_func(1)
    log.info("relay_init")
end

--分发参数到各个子项
local function ac_data_to_chip()
    local reason, slp_state, reason_detail = pm.lastReson() 
    log.info("开机原因",reason_detail)

    local ir_data = ir_chip.get_data()
    for k,v in pairs(ir_data) do
        if nil ~= ac_status[k]then
            ir_data[k] = ac_status[k]
        end
    end
    
    local e_data = e_chip.get_data()
    for k,v in pairs(e_data) do
        if nil ~= ac_status[k]then
            e_data[k] = ac_status[k]
        end
    end

    -- 使用读后清零模式，ep_init需要恢复成ep
    e_chip.recover_with_repower() 
end

sys.subscribe("AC_DATA_TO_CHIP", ac_data_to_chip)

local function ac_data_recover()
    sys.wait(3000)
    log.info("fdb", "all data read start")
    local iter = fskv.iter()
    if iter then
        while 1 do
            local k = fskv.next(iter)
            if not k then
                break
            end
            log.info("fdb ", k, " value ", fskv.get(k))
        end
    end

    for k, v in pairs(ac_status) do
        local value = fskv.get(k)
        if nil ~= value then
            ac_status[k] = value
        end
        log.info("ac_data_recover", k, ac_status[k])
    end
    
    ac_data_to_chip()

end


local function init()
    e_chip.init()
    ir_chip.init()
    ntc.init()
    key.init()
end

local function get_data()
    
    local e_data    = e_chip.get_data()
    ac_status.i     = e_data.i
    ac_status.u     = e_data.u
    ac_status.p     = e_data.p
    ac_status.p_f   = e_data.p_f
    ac_status.ep    = e_data.ep

    ac_status.temp_box      = ntc.get_data()
    ac_status.temp_indoor   = ntc.to_temp_outside(ac_status.temp_box,ac_status.i)+ac_status.temp_offset

    local ir_data       = ir_chip.get_data()
    ac_status.mode      = ir_data.mode
    ac_status.fan       = ir_data.fan
    ac_status.fan_type  = ir_data.fan_type
    ac_status.code      = ir_data.code
    
end


local ac = {
    init = init,
    data_recover = ac_data_recover,
    get_data = get_data,
}
return ac