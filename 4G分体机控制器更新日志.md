<!--
 * @Author: <PERSON>
 * @Date: 2024-11-07 15:53:20
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-16 14:59:37
 * @FilePath: \sc_a_g01\4G分体机控制器更新日志.md
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->

# V1.0.10
1、删除不需要的代码
2、获取三元组的函数，现在都使用表里的内容，不使用函数获取
3、修复网络看门狗没有使用安全重启导致电量倒退的bug
4、现在上线会获取设备数据，并同步
5、优化电量保存的写法，现在使用pulish实现


# V1.0.9.2
1、修复了AT指令获取信号的问题
2、注释部分不使用的代码


# V1.0.9.1
1、修复一个HLC668文件中极难触发的bug


# V1.0.9 
[大更新，固件发生变化，需要制作差分包，使用差分升级]
1. 修改数据参数的储存方式，现在不是用table存入，现在是按k，v存入flash，测试结果存储方式不变；
2. 优化看参数的存储逻辑，具体如下：
    1. compressor_time、using_time一个小时保存一次+安全重启保存；
    2. code、factor、run_power、c_run_power为改变保存；
    3. ep、ep_init为累计保存（颗粒度0.5度）+重启保存；
    4. mode、temp、fan、fan_type 仅重启保存
    5. ntc的last_temp参数，仅重启保存
    6. 重置参数，会清除电量、统计时间，重置run_power、c_run_power

3. 将ac_status已有的参数在初始化时已经列出，后续的调用全部以.key的形式调用，减少后期出错的概率，电能芯片和红外芯片同样如此
4. 去掉了不安全的重启
5. 简化8110的功率比例参数，现在只有factor作为功率的校正系数出现，其它k、等等参数全部删除；
6. 支持云端AT指令，与单相支持的内容稍有不同，具体如下：
    1. 去掉了vfactor和cfactor参数，这两个参数已经用不到了；
    2. 添加factor读取和设定的命令
7. 优化fskv清理原有旧数据的时候，会将保留ep，ep_init，factor，code，compressor_time，using_time，run_power，c_run_power
8. 规范变量命名，temp_box、temp_indoor，云端的box_temp改为temp_box，三相控制器使用，但是不影响，一更换模板，三相是通过序号对应原始数据和设备数据，单相和插座是严格按照变量名称进行对应的；
9. 优化了ntc的外部温度推测问题，现在同步了thingsBoard的计算参数
10. 修复了重置参数时，电量没有被重置的bug
11. 修复host被设置后，刷固件不能上线的问题，现在本固件只能用于阿里云
12. 禁止了三元组、mqtt地址、端口的修改，包括AT指令、阿里云网关命令
13. 开机功率回调，默认开机功率修改为10W，默认压缩机开机功率60W
14. 测试的关闭方法，防止在现场，还在那里测试,通过AT指令实现 AT+TEST=0 不再测试 其它数值会测试
15. 修复上传标签不正确的bug
16. 优化了升级的代码可以跨版本大文件（小于400k）升级，但是目前不能汇报升级进度，只能汇报进度为0


# V1.0.8
1. 修复 在factor修改后，会导致电量猛然变大，V1.0.7有笔误，没修复

# V1.0.7
1. 修复不能及时响应命令bug
2. 默认开机功率阈值 15->8.5W
3. 修复温度计算偏差较大的问题
4. 增加了电参数复测，减少测试时数据出错的可能性
5. using_time,现在定义为空调的开机总时间
6. 修复 在factor修改后，会导致电量猛然变大

# V1.0.6
1. 修复命令公式不支持负号的bug
2. 资源数据发   12|60  含义为，开机功率阈值设置为12W，压缩机运行功率阈值设置为60W

## 命令代号
1. 继电器
2. 开关机
3. 制冷开机
4. 制热开机
5. 模式
6. 温度设定
7. 风速
8. 自动模式开机
9. 除湿开机
10. 一键匹配
11. 设置码库
12. 送风开机
13. 发送通用命令
14. 设置电能、功率比例因数
15. 设置开机功率
16. 设置压缩机运行功率
17. 获取芯片版本号
18. 恢复出厂设置

## bug：
1. 在factor修改后，会直接生效于上电之后的所有电量，导致电量猛然变大，除非先重新上电完再发factor
2. using_time,当前定义为设备的上电时间，应为空调的运行总时间
3. 有时测试的时候会没有读到电压
4. 可能的bug--电量会在升级程序的时候猛增大，从V1.0.4到1.0.6
5. 下发命令后，数据没有立刻上报  --命令回应用的是publish，但是没注册
6. 功率达到700W+，温度计算会明显不准
7. IMEI上传不了  --物模型存储没打开就看不到

## 优化点
1. 有的机子开机功率比较小，需要更小的开机阈值，推荐改为8.5W 


# V1.0.5
1. 修复了重启会使电量异常增加的bug

# V1.0.4
1. 现在温度会经过计算得到，直接测的温度为temp_box
2. 优化温度的变化曲线，在上电状态下，温度不会突变，现在保存了一个last_temp的变量来防止突变
3. 修复开机原因不能识别的bug
4. 非上电开机，现在会不复位HLW8110，会初始化last_temp
5. 新增网关命令restore，可恢复不可设置的参数，比如电量、开机功率，压缩机启动功率，使用时长，压缩机使用时长，调用函数reset_params
6. 新增设备命令，地址18，功能和5一致，一般不配此命令


# V1.0.3
1. 修复了电能不能使用的bug
2. 修复了有时候电参数上传为0的bug，其实是没有读出来
3. 在不能读出电参数的情况下，会记录电能，并复位HLW8110芯片
4. 注释掉了部分测试代码，防止代码过大
