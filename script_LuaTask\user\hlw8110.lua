

local sys = require "sys"


-- 上传到阿里云的测试结果
-- {"p":1.98,"ep":0,"u":220.28,"ir_control_flag":1,"factor":1,"i":0.04,"temp":27.41,"ir_send_receive_flag":1,"result":0}

local debug = 0

local UART_HLW = 1

local frist_init = 0
local hlw8110_busy = 0 --0为不忙，1为忙
local hlw8110_reflash_time = 10000 
-- local hlw8110_read_topic = "HLW8110_READ"
-- local hlw8110_read_topic = "HLW8110_WRITE"
local hlw_data = {
            i=0.0, -- 电流，
            u=0.0, -- 电压，
            p=0.0, -- 功率，
            p_f=0.0, -- 功率因数，
            ep=0.0, --电量，
            ep_init=0.0, --初始化电量
            factor=1,   --校准因子
            freq = 0.0
            }  

local HLW8110_READ_SUCCESS = 0
local HLW8110_READ_FAIL = -1

local K_C = 1.0
local K_V = 1000/1000

FAIL_TIMES = 0


local HLW_UART_RX_DONE = "HLW_UART_RX_DONE"
local hlw_data_queue = {}
local hlw_writeBuff = {}
local hlw_writeBusy = false

local factor_change_flag = 0

local hlw8110_uart_pram = {
    baudrate = 9600,
    dataBits = 8,
    parity = uart.EVEN,
    stopBits = 1
}



local hlw8110_cmd = {
    read    =   0x00,
    write   =   0x80,
    bit_or  =   0xEA
}

local hlw8110_cmd_val = {
    enable_write = 0xE5,
    disable_write = 0xDC,
    set_channel_A = 0x5A,
    chip_reset = 0x96
}




local hlw8110_reg_all = {
    reg_syscon          = {addr = 0x00, len = 2,val = 0},
    reg_emucon          = {addr = 0x01, len = 2,val = 0},
    reg_hfconst         = {addr = 0x02, len = 2,val = 0},
    reg_pastart         = {addr = 0x03, len = 2,val = 0},
    reg_pbstart         = {addr = 0x04, len = 2,val = 0},
    reg_pagain          = {addr = 0x05, len = 2,val = 0},
    reg_pbgain          = {addr = 0x06, len = 2,val = 0},
    reg_phasea          = {addr = 0x07, len = 1,val = 0},
    reg_phaseb          = {addr = 0x08, len = 1,val = 0},
    reg_paos            = {addr = 0x0A, len = 2,val = 0},
    reg_pbos            = {addr = 0x0B, len = 2,val = 0},
    reg_rmsiaos         = {addr = 0x0E, len = 2,val = 0},
    reg_rmsibos         = {addr = 0x0F, len = 2,val = 0},
    reg_ibgain          = {addr = 0x10, len = 2,val = 0},
    reg_psgain          = {addr = 0x11, len = 2,val = 0},
    reg_psos            = {addr = 0x12, len = 2,val = 0},
    reg_emucon2         = {addr = 0x13, len = 2,val = 0},
    reg_dcia            = {addr = 0x14, len = 2,val = 0},
    reg_dcib            = {addr = 0x15, len = 2,val = 0},
    reg_dcic            = {addr = 0x16, len = 2,val = 0},
    reg_sagcyc          = {addr = 0x17, len = 2,val = 0},
    reg_saglvl          = {addr = 0x18, len = 2,val = 0},
    reg_ovlvl           = {addr = 0x19, len = 2,val = 0},
    reg_oialvl          = {addr = 0x1A, len = 2,val = 0},
    reg_oiblvl          = {addr = 0x1B, len = 2,val = 0},
    reg_oplvl           = {addr = 0x1C, len = 2,val = 0},
    reg_int             = {addr = 0x1D, len = 2,val = 0},
                                 
    reg_pfcntpa         = {addr = 0x20, len = 2,val = 0},
    reg_pfcntpb         = {addr = 0x21, len = 2,val = 0},
    reg_angle           = {addr = 0x22, len = 2,val = 0},
    reg_ufreq           = {addr = 0x23, len = 2,val = 0},
    reg_rmsia           = {addr = 0x24, len = 3,val = 0},
    reg_rmsib           = {addr = 0x25, len = 3,val = 0},
    reg_rmsu            = {addr = 0x26, len = 3,val = 0},
    reg_powerfactor     = {addr = 0x27, len = 3,val = 0},
    reg_energy_pa       = {addr = 0x28, len = 3,val = 0},
    reg_energy_pb       = {addr = 0x29, len = 3,val = 0},
    reg_powerpa         = {addr = 0x2C, len = 4,val = 0},
    reg_powerpb         = {addr = 0x2D, len = 4,val = 0},
    reg_powers          = {addr = 0x2E, len = 4,val = 0},
    reg_emustatus       = {addr = 0x2F, len = 3,val = 0},
    reg_peakia          = {addr = 0x30, len = 3,val = 0},
    reg_peakib          = {addr = 0x31, len = 3,val = 0},
    reg_peaku           = {addr = 0x32, len = 3,val = 0},
    reg_instania        = {addr = 0x33, len = 3,val = 0},
    reg_instanib        = {addr = 0x34, len = 3,val = 0},
    reg_instanu         = {addr = 0x35, len = 3,val = 0},
    reg_waveia          = {addr = 0x36, len = 3,val = 0},
    reg_waveib          = {addr = 0x37, len = 3,val = 0},
    reg_waveu           = {addr = 0x38, len = 3,val = 0},
    reg_instanp         = {addr = 0x3C, len = 4,val = 0},
    reg_instans         = {addr = 0x3D, len = 4,val = 0},
                                 
    reg_ie              = {addr = 0x40, len = 2,val = 0},
    reg_if              = {addr = 0x41, len = 2,val = 0},
    reg_rif             = {addr = 0x42, len = 2,val = 0},
    reg_sysstatus       = {addr = 0x43, len = 1,val = 0},
    reg_rdata           = {addr = 0x44, len = 4,val = 0},
    reg_wdata           = {addr = 0x45, len = 2,val = 0},
    reg_sparec9         = {addr = 0x6E, len = 2,val = 0},
    reg_coeff_chksum    = {addr = 0x6F, len = 2,val = 0},
    reg_rmsiac          = {addr = 0x70, len = 2,val = 0},
    reg_rmsibc          = {addr = 0x71, len = 2,val = 0},
    reg_rmsuc           = {addr = 0x72, len = 2,val = 0},
    reg_powerpac        = {addr = 0x73, len = 2,val = 0},
    reg_powerpbc        = {addr = 0x74, len = 2,val = 0},
    reg_powersc         = {addr = 0x75, len = 2,val = 0},
    reg_energyac        = {addr = 0x76, len = 2,val = 0},
    reg_energybc        = {addr = 0x77, len = 2,val = 0},
    reg_trim_rc         = {addr = 0x7C, len = 2,val = 0},
    reg_trim_vref       = {addr = 0x7D, len = 2,val = 0},

}



local function showhex(buf)
    for i = 1, #buf do
        print(string.format("%02X ", buf[i]))
    end
end

--串口接收字符串转化为表
local function str_to_table(str)
    local rx_table = {}
    for i = 1, #str do
        local _, p = pack.unpack(str, 'b', i)
        rx_table[i] = p
    end
    -- print(table.concat(rx_table, " "))
    return rx_table
end

local function hlw8110_rx_date_check(buf)
    local crc = 0
    for i=1, #buf-1 do
        crc = crc + buf[i]
    end
    crc = (~crc) & 0xFF
    if crc == buf[#buf] then 
        return true
    end
    log.info("hlw8110_rx_date_check", "crc err")
    print(string.format("%02X,%02X ", crc, buf[#buf]))
    return false
end

local function hlw8110_tx_date_cal(buf,len)
    local crc = 0x00
    for i=1, len do
        crc = crc + buf[i]
    end
    return ~crc & 0xFF
end

-- 发送函数
local function write(uid, str)
    local i
    hlw_writeBuff = {}
    for i = 1, #str, 8192 do
        table.insert(hlw_writeBuff, str:sub(i, i + 8192 - 1))
    end
    -- log.info("hlw_gmir", "串口缓冲队列的长度:", hlw_writeBusy, #table.concat(hlw_writeBuff))
    local wrf = {}
    for i = 1, #table.concat(hlw_writeBuff) do
        local _, p = pack.unpack(table.concat(hlw_writeBuff), 'b', i)
        wrf[i] = p
    end
    -- print(table.concat(wrf, " "))

    if not hlw_writeBusy then
        hlw_writeBusy = true
        uart.write(uid, table.remove(hlw_writeBuff, 1))
    end
end

--发送数据,数组为入参
local function hlw8110_uart_tx_data(buf, len)
    local send_str = ''
    for i=1,len do 
        send_str = send_str..pack.pack('b', buf[i])
    end
    -- log.info("hlw8110_uart_tx_data", "send_str", #send_str)
    -- showhex(buf)
    write(UART_HLW, send_str)
end


local function hlw8110_write_reg_val(addr, len, val)
    local buf = {}
    local i = 0
    buf[1]  = 0xA5
    buf[2]  = (hlw8110_cmd.write | addr ) & 0xFF
    i = 3
    for j=3, len+2 do
        buf[j] = val >> (8*(len-j+2)) & 0xFF
    end
    i = len+i
    
    buf[i]  = hlw8110_tx_date_cal(buf,i-1)
    -- showhex(buf)
    hlw8110_uart_tx_data(buf,i)
end

local function hlw8110_write_bit_or(bit_or_val)
    local buf = {}
    buf[1]  = 0xA5
    buf[2]  = hlw8110_cmd.bit_or
    buf[3]  = bit_or_val
    buf[4]  = hlw8110_tx_date_cal(buf,3)
    hlw8110_uart_tx_data(buf,4)
end

local function hlw8110_write_reg_EN()
    hlw8110_write_bit_or(hlw8110_cmd_val.enable_write)
end

local function hlw8110_write_reg_DIS()
    hlw8110_write_bit_or(hlw8110_cmd_val.disable_write)
end

local function hlw8110_write_reg_setChannelA()
    hlw8110_write_bit_or(hlw8110_cmd_val.set_channel_A)
end

local function hlw8110_write_reg_chip_reset()
    hlw8110_write_bit_or(hlw8110_cmd_val.chip_reset)
end

local function hlw8110_read_reg(reg,len)
    local buf = {}
    buf[1]  = 0xA5
    buf[2]  = hlw8110_cmd.read|reg
    hlw8110_uart_tx_data(buf,2)
    --等待回应
    local ret,rx_buf = sys.waitUntil(HLW_UART_RX_DONE, 500)
    if ret == false then
        log.info("hlw8110_read_read_reg", "timeout")
        return false
    end
    if #rx_buf ~= len+1 then
        log.info("hlw8110_read_read_reg", "rx_buf len err")
        return false
    end
    if debug ==1 then
        log.info("hlw8110_rx_date_proc", "rx_buf",rx_buf)
    end
    

    local rx_hex = str_to_table(rx_buf)
    -- showhex(rx_hex)
    
    local all_hex = {}
    for i = 1, #buf do
        table.insert(all_hex, buf[i])
    end
    for i = 1, #rx_hex do
        table.insert(all_hex, rx_hex[i])
    end
    
    local crc_result = hlw8110_rx_date_check(all_hex)
    if true ~= crc_result then
        log.info("hlw8110_read_read_reg", "crc err")
        return false
    end
    local val = 0
    for i=1,len do
        val = val*256 + rx_hex[i]
    end
    return val
end

local function hlw8110_read_reg_syscon()
    local val = hlw8110_read_reg(hlw8110_reg_all.reg_syscon.addr, hlw8110_reg_all.reg_syscon.len)
    if type(val) == "number" then
        hlw8110_reg_all.val = val
    end
    if debug == 1 then
        log.info("hlw8110_read_reg_syscon", "val", val)
    end
    
    return val
end

local  function hlw8110_read_reg_one(reg)
    local ret = -1
    local val = hlw8110_read_reg(reg.addr, reg.len)
    if type(val) == "number" then
        reg.val = val
        if debug == 1 then
            log.info("hlw8110_read_reg_one:", (string.format( "0X%02X",reg.addr )),  (string.format( "0X%02X",val )))
        end
        ret = HLW8110_READ_SUCCESS
    else
        -- reg.val = 0
        log.info("hlw8110_read_reg_one:", (string.format( "0X%02X",reg.addr )), "read err")
        ret = HLW8110_READ_FAIL
    end
    return ret
end

local  function hlw8110_read_reg_all()
    for k,v in pairs(hlw8110_reg_all) do
        local val = hlw8110_read_reg(v.addr, v.len)
        if type(val) == "number" then
            v.val = val
            if debug == 1 then
                log.info("hlw8110_read_reg_all:".. k , val)
            end
        else
            log.info("hlw8110_read_reg_all:".. k , "read err")
        end
        sys.wait(50)
    end
end


local function hlw8110_rx_date_proc(buf)
    -- 1. check data

    -- 2. proc data
    -- showhex(buf)
    -- log.info("hlw8110_rx_date_proc", "rx_buf len err")
    -- sys.publish(HLW_UART_RX_DONE, buf)

end


local function hlw8110_uart_init()
    uart.setup(UART_HLW, 
                hlw8110_uart_pram.baudrate, 
                hlw8110_uart_pram.dataBits, 
                hlw8110_uart_pram.stopBits, 
                hlw8110_uart_pram.parity
            )
    sys.subscribe(HLW_UART_RX_DONE, function(uid, data)
        if uid == UART_HLW then
            hlw8110_rx_date_proc(data)
        end
    end)
    -- log.info("hlw8110_uart_init","hlw uart init")
    uart.on(UART_HLW, "receive", function(uid)
        -- log.info("hlw on receive")
        table.insert(hlw_data_queue, uart.read(uid, 8192))
        local rdata = table.concat(hlw_data_queue)
        sys.publish(HLW_UART_RX_DONE, rdata)
        hlw_data_queue = {}
    end)

    uart.on(UART_HLW, "sent", function()
        if #hlw_writeBuff == 0 then
            hlw_writeBusy = false
            if debug == 1 then
                log.info("hlw_gmir", "uart hlw send done")
            end
        else
            hlw_writeBusy = true
            uart.write(UART_HLW, table.remove(hlw_writeBuff, 1))
            -- log.info("hlw_gmir", "uart hlw send ing...")
        end
    end)
end


function hlw8110_judge_checksum_calfactor()
    local d

    -- 读取RmsIAC、RmsIBC、RmsUC、PowerPAC、PowerPBC、PowerSC、EnergAc、EnergBc的值
    hlw8110_read_reg_one(hlw8110_reg_all.reg_rmsiac)
    sys.wait(50)
    hlw8110_read_reg_one(hlw8110_reg_all.reg_rmsibc)
    sys.wait(50)
    hlw8110_read_reg_one(hlw8110_reg_all.reg_rmsuc)
    sys.wait(50)
    hlw8110_read_reg_one(hlw8110_reg_all.reg_powerpac)
    sys.wait(50)
    hlw8110_read_reg_one(hlw8110_reg_all.reg_powerpbc)
    sys.wait(50)
    hlw8110_read_reg_one(hlw8110_reg_all.reg_powersc)
    sys.wait(50)
    hlw8110_read_reg_one(hlw8110_reg_all.reg_energyac)
    sys.wait(50)
    hlw8110_read_reg_one(hlw8110_reg_all.reg_energybc)
    sys.wait(50)
    hlw8110_read_reg_one(hlw8110_reg_all.reg_coeff_chksum)
    sys.wait(50)

    -- log.info("hlw8110_judge_checksum_calfactor", "A通道电流转换系数:", hlw8110_reg_all.reg_rmsiac.val)
    -- log.info("hlw8110_judge_checksum_calfactor", "B通道电流转换系数:", hlw8110_reg_all.reg_rmsibc.val)
    -- log.info("hlw8110_judge_checksum_calfactor", "电压通道转换系数:", hlw8110_reg_all.reg_rmsuc.val)
    -- log.info("hlw8110_judge_checksum_calfactor", "A通道功率转换系数:", hlw8110_reg_all.reg_powerpac.val)
    -- log.info("hlw8110_judge_checksum_calfactor", "B通道功率转换系数:", hlw8110_reg_all.reg_powerpbc.val)
    -- log.info("hlw8110_judge_checksum_calfactor", "视在功率转换系数:", hlw8110_reg_all.reg_powersc.val)
    -- log.info("hlw8110_judge_checksum_calfactor", "A通道电量转换系数:", hlw8110_reg_all.reg_energyac.val)
    -- log.info("hlw8110_judge_checksum_calfactor", "B通道电量转换系数:", hlw8110_reg_all.reg_energybc.val)
    -- log.info("hlw8110_judge_checksum_calfactor", string.format("计算系数校验和:%x\n ", hlw8110_reg_all.reg_coeff_chksum.val) )
   
    local a = ~(0xFFFF 
                +hlw8110_reg_all.reg_rmsiac.val 
                + hlw8110_reg_all.reg_rmsibc.val
                + hlw8110_reg_all.reg_rmsuc.val
                + hlw8110_reg_all.reg_powerpac.val
                + hlw8110_reg_all.reg_powerpbc.val
                + hlw8110_reg_all.reg_powersc.val
                + hlw8110_reg_all.reg_energyac.val
                + hlw8110_reg_all.reg_energybc.val)
            

    U16_CheckSUM_Data = a & 0xFFFF
    -- print(string.format("计算系数校验和:%x\n ", U16_CheckSUM_Data))

    if ( U16_CheckSUM_Data ==  hlw8110_reg_all.reg_coeff_chksum.val) then
        d = 1
        print("校验和正确\r\n ")
    else
        d = 0
        print("校验和出错\r\n ")
    end

    return d
end


local function hlw8110_init()
    hlw8110_uart_init()
    if frist_init == 0  then
        hlw8110_write_reg_chip_reset()
        frist_init = 1
    end
    
    sys.wait(100)
    hlw8110_write_reg_EN()
    sys.wait(10)
    hlw8110_write_reg_setChannelA()
    sys.wait(10)
    
    hlw8110_write_reg_val(hlw8110_reg_all.reg_syscon.addr,hlw8110_reg_all.reg_syscon.len,0x0a04)
    sys.wait(10)

    hlw8110_write_reg_val(hlw8110_reg_all.reg_emucon.addr, hlw8110_reg_all.reg_emucon.len,0x0001)
    hlw8110_write_reg_val(hlw8110_reg_all.reg_emucon2.addr, hlw8110_reg_all.reg_emucon2.len,0x0065)  -- 读完电能清零


    sys.wait(10)

    hlw8110_write_reg_DIS()

    hlw8110_judge_checksum_calfactor()
    sys.wait(10)

    -- --测试写入的数据是否正确
    -- hlw8110_read_reg_one(hlw8110_reg_all.reg_syscon)
    -- sys.wait(50)
    -- hlw8110_read_reg_one(hlw8110_reg_all.reg_emucon)
    -- sys.wait(50)
    -- hlw8110_read_reg_one(hlw8110_reg_all.reg_emucon2)
    -- sys.wait(50)

    

end


local function hlw8110_read_IA()
    local a = 0.0
    local read_ret = hlw8110_read_reg_one(hlw8110_reg_all.reg_rmsia)
    if read_ret ~= HLW8110_READ_SUCCESS then
        log.info("hlw8110_read_IA ", "A通道电流寄存器: ", "read err")
        return HLW8110_READ_FAIL
    end

    -- if debug == 1 then
    --     log.info("hlw8110_read_IA ", "A通道电流寄存器: ", hlw8110_reg_all.reg_rmsia.val)
    -- end
    if (hlw8110_reg_all.reg_rmsia.val & 0x800000) == 0x800000 then
        hlw_data.i = 0;
    else
        -- local a = hlw8110_reg_all.reg_rmsia.val / 0x800000 * hlw8110_reg_all.reg_rmsiac.val

        -- 数太大，要用64位计算
        local d_big = bit64.to64(0x800000)
        local c_big = bit64.multi(bit64.to64(hlw8110_reg_all.reg_rmsia.val),bit64.to64(hlw8110_reg_all.reg_rmsiac.val)) 
        c_big = bit64.multi(c_big,100)   --为了保留小数
        a = bit64.to32(bit64.pide(c_big,d_big))
        a = a/100

        a = a/K_C
        a = a/1000              -- a = 5003ma,a/1000 = 5.003A,单位转换成A
        a = a
        if 0 ~= a then          -- 杜绝异常的0值，若是读取出的0，这里不会相等
            hlw_data.i = a
        end
    end
    if debug == 1 then
        log.info("hlw8110_read_IA ", "A通道电流: ", hlw_data.i)
    end
    return HLW8110_READ_SUCCESS
end

local function hlw8110_read_U()
    local a = 0.0
    local read_ret = hlw8110_read_reg_one(hlw8110_reg_all.reg_rmsu)
    if read_ret ~= HLW8110_READ_SUCCESS then
        log.info("hlw8110_read_U ", "A通道电压寄存器: ", "read err")
        return HLW8110_READ_FAIL
    end

    -- if debug == 1 then
    --     log.info("hlw8110_read_U ", "A通道电压寄存器:", hlw8110_reg_all.reg_rmsu.val)
    -- end
    if (hlw8110_reg_all.reg_rmsu.val & 0x800000) == 0x800000 then
        hlw_data.u = 0;
               
    else
        -- local  a = hlw8110_reg_all.reg_rmsu.val / 0x400000  * hlw8110_reg_all.reg_rmsuc.val

        -- 数太大，要用64位计算
        local d_big = bit64.to64(0x400000)
        local c_big = bit64.multi(bit64.to64(hlw8110_reg_all.reg_rmsu.val),bit64.to64(hlw8110_reg_all.reg_rmsuc.val)) 
        c_big = bit64.multi(c_big,100)   --为了保留小数
        a = bit64.to32(bit64.pide(c_big,d_big))
        a = a/100

        a = a/100           --计算出a = 22083.12mV,a/100表示220.8312V，电压转换成V
        a = a/K_V
        a = a
        if 0 ~= a then     -- 杜绝异常的0值，若是读取出的0，这里不会相等
            hlw_data.u = a
        end
    end
    if debug == 1 then
        log.info("hlw8110_read_U ", "A通道电压:", hlw_data.u)
    end
    return HLW8110_READ_SUCCESS
end

local function hlw8110_read_PA()
    local a = 0.0
    local b = 0
    local readret = hlw8110_read_reg_one(hlw8110_reg_all.reg_powerpa)
    if readret ~= HLW8110_READ_SUCCESS then
        log.info("hlw8110_read_PA ", "A通道功率寄存器: ", "read err")
        return HLW8110_READ_FAIL
    end

    -- if debug == 1 then
    --     log.info("hlw8110_read_PA ", "A通道功率寄存器:", hlw8110_reg_all.reg_powerpa.val)
    -- end

    if   ((hlw8110_reg_all.reg_powerpa.val & 0x80000000)>>31) & 0x01 > 0 then
        log.info("hlw8110_read_PA ", "A通道功率寄存器:", "负功率", hlw8110_reg_all.reg_powerpa.val , 0x80000000, math.floor(0x80000000),
        (hlw8110_reg_all.reg_powerpa.val & 0x80000000)>>31
    )
        b = ~hlw8110_reg_all.reg_powerpa.val;
        a = b;
    else
        b = hlw8110_reg_all.reg_powerpa.val;
    end
    -- 数太大，要用64位计算
    local d_big = bit64.to64(0x80000000)
    local c_big = bit64.multi(bit64.to64(b),bit64.to64(hlw8110_reg_all.reg_powerpac.val)) 
    c_big = bit64.multi(c_big,100)   --为了保留小数
    a = bit64.to32(bit64.pide(c_big,d_big))
    a = a/100

    -- 功率需要分正功和负功计算,U16_AC_P = (U32_POWERPA_RegData * U16_PowerPAC_RegData)/(2^31*电压系数*电流系数)
	-- 单位为W,比如算出来5000.123，表示5000.123W
    a = a/K_C
    a = a/K_V
    a = a * hlw_data.factor
    
    if 0 ~= a then      -- 杜绝异常的0值，若是读取出的0，这里不会相等
        hlw_data.p = a
    end
    

    if debug == 1 then
        log.info("hlw8110_read_PA ", "A通道功率:", hlw_data.p)
    end
    return HLW8110_READ_SUCCESS
end

local function hlw8110_read_EA()
    local a = 0.0
    local b = 0
    local read_ret = hlw8110_read_reg_one(hlw8110_reg_all.reg_energy_pa)
    sys.wait(50)
    if read_ret ~= HLW8110_READ_SUCCESS then
        log.info("hlw8110_read_EA ", "A通道有功电量寄存器: ", "read err")
        return HLW8110_READ_FAIL
    end
    -- if debug == 1 then
    --     log.info("hlw8110_read_EA ", "A通道有功电量寄存器:", (string.format( "0X%02X",hlw8110_reg_all.reg_energy_pa.val )))
    -- end
    
    read_ret = hlw8110_read_reg_one(hlw8110_reg_all.reg_hfconst)
    if read_ret ~= HLW8110_READ_SUCCESS then
        log.info("hlw8110_read_EA ", "HFCONST寄存器: ", "read err")
        return HLW8110_READ_FAIL
    end
    -- if debug == 1 then
    --     log.info("hlw8110_read_EA ", "HFCONST常数:", (string.format( "0X%02X",hlw8110_reg_all.reg_hfconst.val )))
    -- end
    -- a = hlw8110_reg_all.reg_energy_pa.val / 2^20 * hlw8110_reg_all.reg_energyac.val / 2^9 / 4096
    -- 数太大，要用64位计算
    local b_big = bit64.to64(2^29)
    local c_big = bit64.multi(
        bit64.to64(hlw8110_reg_all.reg_hfconst.val),
        bit64.multi(
            bit64.to64(hlw8110_reg_all.reg_energy_pa.val),
            bit64.to64(hlw8110_reg_all.reg_energyac.val))
         ) 
    c_big = bit64.multi(c_big,100)   --为了保留小数
    local d_big = bit64.to64(4096*K_C*K_V)
    local a_big = bit64.pide( 
        bit64.pide(c_big,b_big),
        d_big
    )

    a = bit64.to32(a_big)
    a = a/100  
    a = a * hlw_data.factor
    if a ~= 0 then    -- 杜绝异常的0值，若是读取出的0，这里不会相等
        hlw_data.ep = a + hlw_data.ep_init
        hlw_data.ep_init = hlw_data.ep --读后清零需要赋值
    end
    if debug == 1 then
        log.info("hlw8110_read_EA ", "A通道有功电量:", hlw_data.ep)
    end
    return HLW8110_READ_SUCCESS
end

local function hlw8110_read_LineFreq()
    local a = 0.0
    hlw8110_read_reg_one(hlw8110_reg_all.reg_ufreq)
    -- if debug == 1 then
    --     log.info("hlw8110_read_LineFreq ", "频率寄存器:", hlw8110_reg_all.reg_ufreq.val)
    -- end
    a = hlw8110_reg_all.reg_ufreq.val
    a = 3579545/(8*a)
    if a > 40 and a < 55 then
        hlw_data.freq = 50
    elseif a >= 55 and a < 70 then
        hlw_data.freq = 60
    else
        hlw_data.freq = 0
    end
end

local function hlw8110_read_PF()
    local a = 0.0
    local read_ret = hlw8110_read_reg_one(hlw8110_reg_all.reg_powerfactor)
    if read_ret ~= HLW8110_READ_SUCCESS then
        log.info("hlw8110_read_PF ", "功率因数寄存器: ", "read err")
        return HLW8110_READ_FAIL
    end
    -- if debug == 1 then
    --     log.info("hlw8110_read_LineFreq ", "功率因数寄存器:", hlw8110_reg_all.reg_powerfactor.val)
    -- end
    
    if hlw8110_reg_all.reg_powerfactor.val>0x800000 then  --为负，容性负载
        a = (0xffffff-hlw8110_reg_all.reg_powerfactor.val+1)/0x7fffff
    else
        a = hlw8110_reg_all.reg_powerfactor.val/0x7fffff
    end

    --小于0.3W，空载或小功率，PF不准
    if hlw_data.p < 0.3 then
        a = 0
    end
    -- 功率因素*100，最大为100，最小负100
    hlw_data.p_f = a
    if debug == 1 then
        log.info("hlw8110_read_PF ", "功率因数:", hlw_data.p_f)
    end
    return HLW8110_READ_SUCCESS
end


-- local function test()
--     sys.taskInit(function()
        
--         while true do
            
--             hlw8110_init()
--             -- hlw8110_read_reg_all()
            
--             hlw8110_read_IA()
--             hlw8110_read_U()
--             hlw8110_read_PA()
--             hlw8110_read_EA()
--             hlw8110_read_LineFreq()
--             hlw8110_read_PF()
--             log.info("hlw8110.test", "hlw_data.i", hlw_data.i)
--             log.info("hlw8110.test", "hlw_data.u", hlw_data.u)
--             log.info("hlw8110.test", "hlw_data.p", hlw_data.p)
--             log.info("hlw8110.test", "hlw_data.ep", hlw_data.ep)
--             log.info("hlw8110.test", "hlw_data.freq", hlw_data.freq)
--             log.info("hlw8110.test", "hlw_data.p_f", hlw_data.p_f)
--             sys.wait(5000)
--         end
--     end)
-- end

--设置校准因子
--[[
@description: 设置校准因子
                由于是读后不清零，所以必须
                1、在设定时和设定后到复位前禁止读取
                2、在复位前保存ep到ep_init，

@return {*}
--]]
--
function set_power_factor(factor)
    log.info("set_power_factor", factor)
    while hlw8110_busy == 1 do
        sys.wait(500)
    end
    hlw8110_busy = 1
    hlw_data.factor = factor
    sys.publish("SAVE_ONE_DATA","factor",factor)
    factor_change_flag = 1
    hlw8110_busy = 0
    return true
end


local function read()
    sys.taskInit(function ()
        local ret1,ret2,ret3,ret4,ret5,ret6
        while 1 do
            while (0 == hlw8110_busy) and (0 == factor_change_flag) do
                hlw8110_busy = 1
                ret1 = hlw8110_read_IA()
                sys.wait(50)
                ret2 = hlw8110_read_U()
                sys.wait(50)
                ret3 = hlw8110_read_PA()
                sys.wait(50)
                ret4 = hlw8110_read_EA()
                sys.wait(50)
                ret5 = hlw8110_read_LineFreq()
                sys.wait(50)
                ret6 = hlw8110_read_PF()
                sys.wait(50)
                hlw8110_busy = 0
                break
            end
            if hlw_data.u == 0                  --一般不会出现相等的情况
                or hlw_data.i == 0 
                or hlw_data.p == 0
                or ret1+ret2+ret3 < -1          -- 读取有1个失败
                or 0 ~= factor_change_flag      -- 因子改变
            then
                log.info("hlw8110_read", hlw_data.u, hlw_data.i, hlw_data.p)
                log.info("hlw8110_read", "data err, reinit")
                if hlw_data.ep > hlw_data.ep_init then
                    hlw_data.ep_init = hlw_data.ep
                end
                frist_init = 0
                sys.wait(1000)
                hlw8110_init()
                if 0 ~= factor_change_flag  then
                    factor_change_flag = 0
                end
            else
                sys.waitUntil("HLW8110_READ",hlw8110_reflash_time)
            end
            
            -- sys.wait(hlw8110_reflash_time)
        end
    end)
    
end




--由于读取后不清零（防止某次读不到，数据也没有了），所以在保存的时候不能把电量同步到初始化电量中
--上电时恢复参数,上电的时候把上次的电量统计到初始化电量中
local function recover_with_repower()
    hlw_data.ep_init = hlw_data.ep
end

--复位时恢复参数，复位的时候由于不复位HLW8110，所以不需要把电量统计到初始化电量中
local function recover_with_reset()
    
end
local ep_save_step = 0.5
local ep_init_last = 0
local ep_last = 0
local function ep_save_task()
    ep_init_last = hlw_data.ep_init
    ep_last = hlw_data.ep
    sys.taskInit(function()
        while true do
            sys.wait(60000)
            if hlw_data.ep_init - ep_init_last > ep_save_step then
                log.info("hlw8110_ep_save", "ep_init", hlw_data.ep_init)
                ep_init_last = hlw_data.ep_init
                sys.publish("SAVE_ONE_DATA", "ep_init",hlw_data.ep_init)
            end
            if hlw_data.ep - ep_last > ep_save_step then
                log.info("hlw8110_ep_save", "ep", hlw_data.ep)
                ep_last = hlw_data.ep
                sys.publish("SAVE_ONE_DATA", "ep",hlw_data.ep)
            end
        end
    end
    )
    
end
local function init()
    sys.taskInit(function ()
        hlw8110_init()
        ep_save_task()
        read()
    end)
end
local function get_data()
    return hlw_data
end


local function set_ep(val)
    local a = tonumber(val)
    if "number"  == type(a) then
        hlw_data.ep = a
        hlw_data.ep_init = a
        sys.publish("SAVE_ONE_DATA", "ep",hlw_data.ep)
        sys.publish("SAVE_ONE_DATA", "ep_init",hlw_data.ep_init)
        return true
    else
        return false
    end
end
    

hlw8110 = {
    init = init,
    -- test = test,
    -- read = read,
    -- read_once = read_once,
    -- write = write,       
    get_data = get_data,
    recover_with_repower = recover_with_repower,
    recover_with_reset = recover_with_reset,
    set_power_factor = set_power_factor,
    set_ep = set_ep
}

return hlw8110  -- 返回hlw8110模块