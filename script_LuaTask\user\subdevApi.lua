local aliyun = require "aliyun"
local equips = require "equipManager"
local ir_chip = require "hlc668"
local e_chip = require "hlw8110"
local ac = require "ac_conditioner"
local aliyunota = require"aliyunota"
local key       = require "key"
local sys = require "sys"
local ntc = require "ntc"
local at_exec = require "at_exec"


local ALI_PRODUCT_KEY = "a19NCfTXMmx"
local ALI_PRODUCT_SECRET = "BUu4dCwoqfw2fXy1"
local REGION_ID = "cn-shanghai"
local CYCLE = 60

local START_FLAG = 0
local fast_up_times = 0
local last_up_time = 0


--硬件类型
HardType = {
    {
        ["attrKey"] = "HardType",
        ["attrValue"] = _G.PROJECT
    }
}

local last_test_result = 0

--阿里云客户端是否处于连接状态
sConnected = false
--消息发布次数
local publishCnt = 1
local thing_request_id = 0
--主题table
local topics = {}
local eventTopics = {}
local services = {"CMD", "Reboot"}

--配置标记
local GATEWAY_CONFIG_FLAG = 0
local EQUIP_CONFIG_FLAG = 0
local EQUIP_DATA_FLAG = 0
--命令响应相关的全局变量
local CMDID = nil
local TASKID = nil


local SYS_TICK = 0

local last_store_time =  0

local function num2point(x)
    return x-x%0.01
end

--根据自己的服务器修改以下参数
local tPara = {
    Registration = false,            --是否是预注册 已预注册为false  true or false,
    DeviceName = "",                --设备名称
    ProductKey = "",                --产品key
    ProductSecret = "",             --产品secret
    DeviceSecret = "",              --设备secret
    mqtt_host = "",                 --如果没有注册需要填写实例id，在实例详情页面
    mqtt_port = 1883,               --mqtt端口
    mqtt_isssl = true,              --是否使用ssl加密连接，true为无证书最简单的加密
    InstanceId = "",               --实例id
 }

tPara.ProductKey = fskv.get("product_key") or "a19NCfTXMmx"
tPara.DeviceName = fskv.get("device_name") or mobile.imei()
tPara.ProductSecret = fskv.get("product_secret") or "BUu4dCwoqfw2fXy1"
tPara.InstanceId = fskv.get("instance_id") or "iot-060a57j1"
tPara.DeviceSecret = fskv.get("device_secret")
tPara.mqtt_host = tPara.InstanceId..".mqtt.iothub.aliyuncs.com"
tPara.mqtt_port = 1883



 local socketsoftdog = sys.taskInit(function ()      -- 网络业务看门狗
    while true do
        if sys.wait(360000) ==nil then--等待30秒，没有喂狗重启
            log.info("userServiceRebootCb","网络业务逻辑看门狗重启")
            sys.publish("REBOOT_SAFE")
        end
    end
end)



-- 网关Topic初始化
local function topicsInit(productKey, deviceName)
    topics.__thing_topic_version_post = string.format("/ota/device/inform/%s/%s", productKey, deviceName)
    topics.__thing_topic_ota_get = string.format("/ota/device/upgrade/%s/%s", productKey, deviceName)

    topics.__thing_topic_prop_post = string.format("/sys/%s/%s/thing/event/property/post", productKey, deviceName)
    topics.__thing_topic_prop_post_reply = topics.__thing_topic_prop_post .. "_reply"
    topics.__thing_topic_prop_set = string.format("/sys/%s/%s/thing/service/property/set", productKey, deviceName)
    topics.__thing_topic_prop_set_reply = topics.__thing_topic_prop_set .. "_reply"
    topics.__thing_topic_prop_get = string.format("/sys/%s/%s/thing/service/property/get", productKey, deviceName)
    topics.__thing_topic_prop_get_reply = topics.__thing_topic_prop_get .. "_reply"
    topics.__thing_topic_update_device_info_up = string.format("/sys/%s/%s/thing/deviceinfo/update", productKey, deviceName)
    topics.__thing_topic_update_device_info_up_reply = topics.__thing_topic_update_device_info_up .. "_reply"
    topics.__thing_topic_event_post_pattern = "/sys/%s/%s/thing/event/%s/post"
    topics.__thing_topic_service_pattern = "/sys/%s/%s/thing/service/%s/post"
    topics.__mqtt_config = string.format("/%s/%s/user/push", productKey, deviceName)
    topics.__mqtt_equip_data = string.format("/%s/%s/user/data", productKey, deviceName)
    topics.__mqtt_ntp_request = string.format("/ext/ntp/%s/%s/request", productKey, deviceName)
    topics.__mqtt_ntp_response = string.format("/ext/ntp/%s/%s/response", productKey, deviceName)
    topics.__mqtt_cmd_reply = string.format("/%s/%s/user/cmdReply", productKey, deviceName)
    topics.__thing_topic_services = {}
    for k, v in pairs(services)
    do
        table.insert(topics.__thing_topic_services,
            string.format(topics.__thing_topic_service_pattern, productKey, deviceName, v))
    end
end





--生成请求ID
local function thingGetRequestId()
    thing_request_id = thing_request_id + 1
    return thing_request_id
end


--[[
函数名：publishCb
功能  ：发布成功的回调
参数  ：
para：调用mqttclient:publish时传入的para
result：true表示发布成功，false或者nil表示失败
]]
local function publishCb(result, para)
    log.info("publishCb", para, result)
    publishCnt = publishCnt + 1
end

--响应服务调用
local function thingAnswerService(identifier, request_id, code, data)
    local response = {
        ["id"] = request_id,
        ["code"] = code,
        ["data"] = data
    }
    local service_reply_topic = string.format(topics.__thing_topic_service_pattern, tPara.ProductKey, tPara.DeviceName, identifier) .. "_reply"
    aliyun.publish(service_reply_topic, 1, json.encode(response), publishCb, "publishTest".. publishCnt)
end

-- --属性获取处理函数
-- local function userPropertyGetEventHandle()
-- end

-- --节点命令处理函数
-- local function userServiceCmdCb()
-- end

--设备复位回调函数
local function userServiceRebootCb()
    log.info("userServiceRebootCb","用户软件重启")
    sys.publish("REBOOT_SAFE")
end


--[[
函数名：subscribeSysTopic
功能  ：网关订阅系统消息
参数  ：无
返回值：无
]]
local function subscribeSysTopic()
    local subscribe_sys_topics = {}
    table.insert(subscribe_sys_topics, {topics.__thing_topic_prop_set, 0})
    table.insert(subscribe_sys_topics, {topics.__thing_topic_prop_get, 0})
    -- table.insert(subscribe_sys_topics, {topics.__thing_topic_raw_down, 0})
    table.insert(subscribe_sys_topics, {topics.__thing_topic_prop_post_reply, 0})
    table.insert(subscribe_sys_topics, {topics.__thing_topic_update_device_info_up_reply, 0})
    table.insert(subscribe_sys_topics, {topics.__mqtt_config, 0})
    table.insert(subscribe_sys_topics, {topics.__mqtt_ntp_response, 0})
    table.insert(subscribe_sys_topics, {topics.__thing_topic_ota_get, 0})
    for k, v in pairs(topics.__thing_topic_services)
    do
        table.insert(subscribe_sys_topics, {v, 0})
    end
    for k, v in pairs(subscribe_sys_topics)
    do
        log.info("forwaytech", v[1], v[2])
        aliyun.subscribe(v[1], v[2])
    end
end


--网关更新标签
local function thingUpdateTags(tags)
    local topic = topics.__thing_topic_update_device_info_up
    local request_params = tags
    local request_id = thingGetRequestId()
    local request = {
        ["id"] = request_id,
        ["version"] = "1.0",
        ["params"] = request_params,
        ["method"] = "thing.deviceinfo.update"
    }
    aliyun.publish(topic, 1, json.encode(request), publishCb, "publishTest".. publishCnt)
end


--[[
函数名：thingPostProperty
功能  ：属性上报
参数  ：@string property_data,属性数据
返回值：无
]]
local function thingPostProperty(property_data)
    local request_params = property_data
    local request_id = thingGetRequestId()
    local request = {
        ["id"] = request_id,
        ["version"] = "1.0",
        ["params"] = request_params,
        ["method"] = "thing.event.property.post"
    }
    aliyun.publish(topics.__thing_topic_prop_post, 1, json.encode(request), publishCb, "publishTest".. publishCnt)
end

local function gatewayPostVersion()
    local gVersion = PROJECT.."-V"..VERSION
    local request_id = thingGetRequestId()
    local request = {
        ["id"] = request_id,
        ["params"] = {
            ["version"] = gVersion
        }
    }
    aliyun.publish(topics.__thing_topic_version_post, 1, json.encode(request), publishCb, "gatewayPostVersion ".. publishCnt)
end

--[[
函数名：gatewayPostBaseInfo
功能  ：属性上报,包含第三方平台属性
参数  ：无
返回值：无
]]
local function gatewayPostBaseInfo()
    local request_params = {
        ["ICCID"] = mobile.iccid(),
        ["IMEI"] = mobile.imei(),
        ["SQ"] = tostring(mobile.csq()),
    }
    -- log.info("gatewayPostBaseInfo", "payload:", json.encode(request_params))
    if sConnected then
        thingPostProperty(request_params)
    end
end


-- 子设备上报属性
local function equipPostProperty(property_data)
    local payload = json.encode(property_data)
    -- log.info("equipPostProperty", payload)
    aliyun.publish(topics.__mqtt_equip_data, 1, payload, publishCb, "equipPostProperty".. publishCnt)
end


--请求时间同步
local function gatewayRequestNtp()
    local request_params = {
        ["deviceSendTime"] = tostring(os.time()*1000)
    }    
    local payload = json.encode(request_params)
    log.info("gatewayRequestNtp", "request send")
    aliyun.publish(topics.__mqtt_ntp_request, 0, payload, publishCb, "gatewayRequestNtp")
end

--子设备上报命令响应
local function equipCmdReply(ret)
    if CMDID ~= nil and TASKID ~= nil then
        local reply = {
            ["cmdId"] = CMDID,
            ["taskId"] = TASKID,
            ["result"] = tostring(ret)
        }
        aliyun.publish(topics.__mqtt_cmd_reply, 1, json.encode(reply),  publishCb, "equipCmdReply" .. publishCnt)
    end
end


--请求配置
local function requestCloudConfig(productKey, deviceName, method)
    local topic = string.format("/%s/%s/user/request", productKey, deviceName)
    local request = {}
    request["method"] = method
    request["productKey"] = productKey
    request["deviceName"] = deviceName
    request["time"] = tostring(math.floor(os.time()/1000))
    aliyun.publish(topic, 1, json.encode(request), publishCb, "requestCloudConfig:" .. method)
end


--[[
函数名：thingPostProperty
功能  ：AT结果上报
参数  ：@string property_data,属性数据
返回值：无
]]

local function ATcmdReply(data)
    local request_params = {
        ["AT"] = data,
    }
    thingPostProperty(request_params)
end

-- MQTT 虚拟AT处理
local function app_procmd_by_mqtt(str_recv)
    local str_rsp = at_exec.app_procmd(str_recv)
    if str_rsp ~="" then
        ATcmdReply(str_rsp)
        sys.publish("FAST_UP", 1) 
    end

end




--属性设置处理函数
local function userPropertySetEventHandle(params)

    if params["AT"] then
        sys.taskInit(function()
            app_procmd_by_mqtt(params["AT"])
            sys.wait(100)
        end)
    end

end



-- 重写result标志位，重启实现再次测试
local function test_again()
    local test_result = fskv.get('test_result')
    test_result['result'] = 1
    fskv.set('test_result',test_result) 
end

-- 命令格式{"cmdMethod":"gatewayCmd","cmdName":"reboot"}
--指令调用回调函数
local function userServiceRequestEventHandle(params)
    -- log.info("forwaytech", params)
    local identifier = ""
    local method = params["method"]
    if string.find(method, "CMD") then
        identifier = "CMD"
        log.info("forwaytech", "CMD set")
        local cmdMethod = params["params"]["cmdMethod"]
        if cmdMethod == "gatewayCmd" then
            local cmdName = params["params"]["cmdName"]
            local cmdParam = 1
            if nil ~= params["params"]["cmdParam"] then
                cmdParam = tonumber(params["params"]["cmdParam"])
            else
                log.info("forwaytech", "Reboot set")
            end
            if cmdName == "reboot" then 
                log.info("forwaytech", "Reboot set")
                userServiceRebootCb()
            elseif cmdName == "reset" then
                userServiceRebootCb()
            elseif cmdName == "restore" then
                reset_params()
                sys.publish("REBOOT_SAFE")
            elseif cmdName == "testAgain" then
                sys.taskInit(function ()
                    test_again()
                    sys.wait(1000)
                    userServiceRebootCb() 
                end)
                
            elseif cmdName == "set_power_factor" then
                sys.taskInit(function ()
                    set_power_factor(cmdParam)
                end)
            elseif cmdName == "set_power_factor_testAgain" then
                sys.taskInit(function ()
                set_power_factor(cmdParam)
                test_again()
                sys.wait(1000)
                userServiceRebootCb()
                end)
            end
        elseif cmdMethod == "equipCmd" then
            log.info("forwaytech", "equipCmd")
            CMDID = params["params"]["cmdId"]
            TASKID = params["params"]["taskId"]
            log.info("forwaytechCMDID", CMDID)
            log.info("forwaytechTASKID", TASKID)
            sys.taskInit(equips.equip_proc_command, params["params"])
        end
    else
        log.info("forwaytech", "unknown service")
    end
    thingAnswerService(identifier, thingGetRequestId(), 200, {})
end




local function update_temp_and_rssi()
    --读取温湿度
    local temp = Get_Temp()
    if temp then
    ac_status.temp_box = ntc.get_data()
    ac_status.temp_indoor = ntc.to_temp_outside(temp, ac_status.i)+ac_status.temp_offset
    ac_status.CPU_TEMP = ntc.get_cpu_temp()
    end

    ac_status.rssi = mobile.csq()
end


--更新数据
local function update_ac_data()

    local now_time = math.floor(os.time())
    if last_store_time == 0 then
        last_store_time = now_time
    end
    local n = now_time - last_store_time
    last_store_time = now_time


    
    local e_data = e_chip.get_data()
    local ir_data = ir_chip.get_data()
    
    for k,v in pairs(e_data) do
        if nil ~= ac_status[k] then
            ac_status[k] = v
            -- log.info("update_ac_data", k, v)
        end
    end

    for k,v in pairs(ir_data) do
        if nil ~= ac_status[k] then
            ac_status[k] = v
            -- log.info("update_ac_data", k, v)
        end
    end

    ac_status.temp_box = ntc.get_data()
    -- log.info("update_ac_data", "temp_box", ac_status.temp_box)
    ac_status.temp_indoor =  ntc.to_temp_outside(ac_status.temp_box,ac_status.i)+ ac_status.temp_offset

    -- log.info("update_ac_data", "temp_indoor", ac_status.temp_indoor)

    if ac_status.p > ac_status.c_run_power then  
        ac_status.runningstatus = 1
        ac_status.compress_status = 1
        ac_status.compressor_time = ac_status.compressor_time + n
        ac_status.current_running_time = ac_status.current_running_time + n        
    elseif ac_status.p > ac_status.run_power then
        ac_status.current_running_time = 0        
        ac_status.runningstatus = 1
        ac_status.compress_status = 0
    else 
        ac_status.current_running_time = 0        
        ac_status.runningstatus = 0
        ac_status.compress_status = 0
    end

    ac_status.power_status = 1

    -- using_time 定义为开机的总时长
    if ac_status.runningstatus == 1 then
        ac_status.using_time = ac_status.using_time + n
    end
    
    return 0
end

-- 测试结果关键字test_result
local function GateWayPostTestResult()
    if  0 ~= last_test_result then
        -- log.info("GateWayPostTestResult ", tostring(json.encode(fskv.get("test_result"))))
        local property_data = {
            ["test_result"] = tostring(json.encode(fskv.get("test_result")))
        }
        if sConnected then
            thingPostProperty(property_data)
        end
    else
        log.info("GateWayPostTestResult", "last test passed ,not post test result")
    end
end

local gatewayPostProperty_busy  = 0


local function gatewayPostProperty()
    -- log.info("gatewayPostProperty ", tostring(json.encode(ac_status)))
    local property_data = {
        ["resource"] = tostring(json.encode(ac_status))
    }
    if sConnected then
        thingPostProperty(property_data)
    end  

end

sys.subscribe("UPLOAD_RESOURCE",gatewayPostProperty)
-- 返回最后一次测试结果，没测和没通过返回false，通过返回true
local function get_last_test_result()
    local test_result = fskv.get('test_result')
    if nil == test_result then
        log.info("test_or_not","without test")
        last_test_result = 1
        return false
    -- 测试结果必须存在且为0，视为通过
    elseif  nil == test_result['result'] or 0 ~= test_result['result'] then 
        last_test_result = 1
        log.info("test_or_not","last test result:", test_result['result'])
        log.info("test_or_not","last test not passed")
        return false
    else       
        last_test_result = test_result['result']
        log.info("test_or_not","last test passed")
        return true
    end
end


--[[
@description: 保存一个数据到flash
@return {*}
--]]
local function save_one_data(k,v)
    if v ~= fskv.get(k) then
        fskv.set( k, v)
        log.info("save_one_data", k,v)
    end
end

sys.subscribe("SAVE_ONE_DATA", save_one_data)


-- 测试各项参数并汇报结果，还没添加汇报结果和保存遥控的代码
-- force_test：强制测试入参，1：必定进行测试，直接执行可能有未知问题，不建议使用；0，上次测试通过就不测试了
local function airconditionor_test(force_test)

    local ret = 0

    if true == get_last_test_result() and 0 == force_test then
        return true
    end

    sys.publish("HLW8110_READ")
    -- hlw8110.read_once()
    sys.wait(1000)
    local hlw_data = hlw8110.get_data()
    if 0 == hlw_data.u then
        sys.publish("HLW8110_READ")
        sys.wait(2000)
        hlw_data = hlw8110.get_data()
    end
    if hlw_data.u > 200 and hlw_data.u < 270 then    
        log.warn("电压：", hlw_data.u, ", 计量测试通过")
    else
        log.warn("电压：", hlw_data.u, ", 计量测试不通过")
        ret=ret+1
    end

    local test_result={
        ['temp'] = 0,
        ['u'] = num2point(hlw_data.u),
        ['i'] = num2point(hlw_data.i),
        ['p'] = num2point(hlw_data.p),
        ['ep'] = num2point(hlw_data.ep),
        ['result'] = 1,
        ['ir_control_flag']= 0,
        ['ir_send_receive_flag']= 0,
    }
    
    local temp = Get_Temp()
    if temp > 5 and temp < 40 then
        log.warn("环境温度", temp, ", 温度测试通过")
    else
        log.warn("环境温度", temp, ", 温度测试不通过")
        ret=ret+1
    end
    test_result['temp'] = num2point(temp)
    

    bsp_ir_chip_air_cond_test_init()
    -- 这两个标志位为0为不通过
    test_result['ir_control_flag'] = ir_control_flag
    test_result['ir_send_receive_flag'] = ir_send_receive_flag
    if 0==ir_control_flag then
        ret=ret+1
    end
    if 0==ir_send_receive_flag then
        ret=ret+1
    end

    test_result['result'] = ret
    test_result['factor'] = hlw_data.factor
    -- log.info('airconditionor_test','temp:',test_result.temp)
    -- log.info('airconditionor_test','u:',test_result.u)
    -- log.info('airconditionor_test','i:',test_result.i)
    -- log.info('airconditionor_test','p:',test_result.p)
    -- log.info('airconditionor_test','ep:',test_result.ep)
    -- log.info('airconditionor_test','factor:',test_result.factor)
    -- log.info('airconditionor_test','ir_control_flag:',test_result.ir_control_flag)
    -- log.info('airconditionor_test','ir_send_receive_flag:',test_result.ir_send_receive_flag)
    -- log.info('airconditionor_test','result:',test_result.result)

    fskv.set('test_result',test_result)

    sys.publish("AC_TEST_FINISHED")
end

sys.subscribe("AC_TEST_START", function ()
    sys.taskInit(function ()
        airconditionor_test(0)
        sys.waitUntil("AC_TEST_FINISHED",6000)
    end)
end)

--计算使用时间，并且保存参数
local function cal_times_and_store_param()
    -- update_ac_data()
    -- 只存这几个数据
    local ac_data = {

        ["compressor_time"]=ac_status.compressor_time,
        ["using_time"]=ac_status.using_time

    }

    for k,v  in pairs(ac_data) do
        save_one_data(k,v)
        log.info("save data", k,v)

    end


    log.info("cal_times_and_store_param", "store ac_data")
end





--向云端发送设备数据
local function upload_data()
    local ac_data = {
        ["runningstatus"]       = ac_status.runningstatus,
        ['compressor_status']   = ac_status.compress_status,
        ['mode']                = ac_status.mode,
        ['temp_set_point']      = ac_status.temp,
        ['fan_speed']           = ac_status.fan,
	    ['temp_box'] 		    = ac_status.temp_box,
        ['temp_indoor'] 	    = ntc.to_temp_outside(ac_status.temp_box, ac_status.i)+ac_status.temp_offset,
        ['temp_offset'] 	    = ac_status.temp_offset,
        ['humi_indoor']         = ac_status.humi,
        ['ep']                  = ac_status.ep,
        ['compressor_time']     = ac_status.compressor_time/3600,
        ['current_running_time']= ac_status.current_running_time/60,
        ['using_time']          = ac_status.using_time/3600,
        ['compressor_voltage']  = ac_status.u,
        ['compressor_current']  = ac_status.i,
        ['pt']                  = ac_status.p,
        ['p_f']                 = ac_status.p_f,
        ['rssi']                = ac_status.rssi,
        ['factor']              = ac_status.factor,
        ['power_status']        = ac_status.power_status,
        ["fan_type"]            = ac_status.fan_type,
        ['code']                = ac_status.code

    }

    equips.equips_update_properties(ac_data)

end

sys.subscribe("UPLOAD_DATA",upload_data)

local function update_fast_up_times(times)  
    fast_up_times = times
end

-- 定时上传任务
local function upload_task(interval)
    while true do 
        local change_flag = 0
        local last_run_status = ac_status.runningstatus

        if update_ac_data() == 0 then    --更新数据
            if last_run_status ~= ac_status.runningstatus then   -- 开关状态发生变更
                change_flag = 1
            end

            local now_time = math.floor(os.time())
            local t1 = interval
            if fast_up_times > 0 then
                t1 = 3
            end

            if (now_time - last_up_time > t1) or (change_flag == 1) then
                update_temp_and_rssi()
                gatewayPostProperty()
                sys.publish("UPLOAD_DATA")
                print(string.format("now time %d t1 %d  last_uptime %d", now_time, t1, last_up_time))
                last_up_time = now_time
                if fast_up_times > 0 then 
                    fast_up_times = fast_up_times -1
                end
            end
        end
        sys.wait(1000)
    end   
end

--配置保存函数
--2022/01/07修改，防止重入
local function userSaveCloudConfigHandle(params)
    local method = params["method"]
    log.info("forwaytech", method)
    if method == "gatewayConfig" then
        if params["gateway"] then   
            if params["gateway"]['dataCycle'] then   
                CYCLE = tonumber(params["gateway"]['dataCycle'])
                GATEWAY_CONFIG_FLAG = 1
            end
        end
    elseif method == "equipConfig" then
        if EQUIP_CONFIG_FLAG == 0 then
            if params["equips"] ~= nil then
                equips.equips_add(params["equips"])
                log.info("forwaytech", "equips save")
                EQUIP_CONFIG_FLAG = 1
                sys.subscribe("FAST_UP", update_fast_up_times)  --need updata then upload fast up times
            end
        end
    elseif method == "dataConfig" then
        if EQUIP_DATA_FLAG == 0 then
            if params["equipDatas"] ~= nil then
                equips.equips_data_to_resource(params["equipDatas"])
                log.info("forwaytech", "equips data save")
                EQUIP_DATA_FLAG = 1
                sys.subscribe("FAST_UP", update_fast_up_times)
            end
        end
    else
        log.info("forwaytech", "unknown config")
    end
end


--同步时间
local function ntp_save(params)
    if params ~= nil then
        local serverSendTime = tonumber(params["serverSendTime"])
        local serverRecvTime = tonumber(params["serverRecvTime"])
        local deviceSendTime = tonumber(params["deviceSendTime"])

        local t = math.floor((serverSendTime + serverRecvTime + os.time()*1000 - deviceSendTime )/2)
        log.info("ntp_save", t)
        local ntim = os.date("*t", math.floor(t/1000))
        -- misc.setClock(ntim)
        rtc.set(ntim)
    end 
end


-- 接收回调
local function rcvCbFnc(topic, payload)
    -- log.info("forwaytech", "testaliyun.rcvCbFnc", topic, payload)
    --根据主题进行消息的分发
    --对于某些主题，需要通过publish达到同步的效果
    log.info("debug", "rcvCbFnc\r\n")
    if string.find(topic, "CMD", 1) then
        local params, result, errinfo = json.decode(payload)
        userServiceRequestEventHandle(params)
    elseif string.find(topic, "user/push", 1) then
        log.info("forwaytech", "user/push")
        local params, result, errinfo = json.decode(payload)
        userSaveCloudConfigHandle(params)
    elseif string.find(topic, "service/property/set", 1) then
        local params, result, errinfo = json.decode(payload)
        userPropertySetEventHandle(params["params"])
    elseif string.find(topic, "property/post_reply", 1) then
        --业务喂狗,如果不通，重启
        coroutine.resume(socketsoftdog,"feed")
        log.info("网络业务成功喂狗")
    elseif string.find(topic, "ext/ntp", 1) then
        log.info("forwaytech", "ext/ntp")
        local params, result, errinfo = json.decode(payload)
        ntp_save(params)
    elseif string.find(topic, "/ota/device/upgrade/", 1) then
        log.info("aliyunota", "ota upgrade")
        aliyunota.upgrade(payload)
    else
        log.info("forwaytech", "unknown topic")
    end
end


--请求云端配置
local function requestCloudConfigs(productKey, deviceName)
    log.info("forwaytech", "requestCloudConfig")
    while (GATEWAY_CONFIG_FLAG == 0)
    do
        requestCloudConfig(productKey, deviceName, "gatewayConfig")
        sys.wait(2000)
    end
    while (EQUIP_CONFIG_FLAG == 0)
    do
        requestCloudConfig(productKey, deviceName, "equipConfig")
        sys.wait(2000)
    end
    
    while (EQUIP_DATA_FLAG == 0)
    do
        requestCloudConfig(productKey, deviceName, "dataConfig")
        sys.wait(2000)
    end
    
    
    --订阅设备属性上报
    sys.subscribe("EQUIPDATA", equipPostProperty)
    sys.subscribe("CMDREPLY", equipCmdReply)
    sys.taskInit(key.Ledtask)
    sys.taskInit(upload_task, CYCLE)
    last_store_time = math.floor(os.time())
end

--- 连接结果的处理函数
-- @bool result，连接结果，true表示连接成功，false或者nil表示连接失败
local function connectCbFnc(result)
    log.info("forwaytech", "testaliyun.connectCbFnc", result)
    
    sConnected = result
    if result then
        --初始化主题
        topicsInit(tPara.ProductKey, tPara.DeviceName)
        --订阅主题，不需要考虑订阅结果，如果订阅失败，aliyun库中会自动重连
        subscribeSysTopic()
        --注册数据接收的处理函数
        aliyun.on("receive", rcvCbFnc)
        
        --上传网关基础信息
        gatewayPostBaseInfo()

        -- 上传程序版本信息
        gatewayPostVersion()

        -- 上传网关的测试结果
        GateWayPostTestResult()

        --更新网关标签
        thingUpdateTags(HardType)

        --请求时间戳
        gatewayRequestNtp()

        --设置升级信息
        aliyunota.deviceName = tPara.DeviceName
        aliyunota.productKey = tPara.ProductKey
        aliyunota.sConnected = sConnected


        --请求网关配置
        if START_FLAG == 0 then
            sys.taskInit(requestCloudConfigs, tPara.ProductKey, tPara.DeviceName)
            START_FALG = 1
        end
    else
        log.info("userServiceRebootCb","连接失败 等待重连")
    end
end


--网关发布信号
local function GateWayPostRSSI()
    local rssi = tostring(mobile.csq())
    local property_data = {
        ["SQ"] = rssi,
        ["CPU_TEMP"] = ntc.get_cpu_temp(),
    }
    if sConnected then
        thingPostProperty(property_data)
    end
end


--网关发布信号
local function ntp_sync()
    if sConnected then
        gatewayRequestNtp()
    end
end

local function save_ir_data()
    sys.wait(10)
    save_one_data("fan", ac_status.fan)
    sys.wait(10)
    save_one_data("fan_type", ac_status.fan_type)
    sys.wait(10)
    save_one_data("mode", ac_status.mode)
    sys.wait(10)
    save_one_data("temp", ac_status.temp)
end

local function save_e_data()
    sys.wait(10)
    save_one_data("ep", ac_status.ep)
    sys.wait(10)
    save_one_data("ep_init", ac_status.ep_init)
end

local function save_ntc_data()
    sys.wait(10)
    save_one_data("last_temp", ntc.get_last_temp())
end

local function reboot_safe()
    sys.taskInit(function ()
        cal_times_and_store_param()
        save_ir_data()
        save_e_data()
        save_ntc_data()
        sys.wait(5000)
        rtos.reboot()
    end)
end

sys.subscribe("REBOOT_SAFE", reboot_safe)





--初始化
aliyun.on("connect",connectCbFnc)
aliyun.on("receive",rcvCbFnc)


--采用一机一密认证方案时：
--配置：ProductKey、获取DeviceName的函数、获取DeviceSecret的函数；其中aliyun.setup中的第二个参数必须传入nil
sys.taskInit(
    function()
        ac.data_recover()
        ac.init()

        airconditionor_test(0)                  --阻塞测试     
        sys.waitUntil("AC_TEST_FINISHED", 6000)          --等待测试完成
        
	    local failed_times = fskv.get("failed_times")
        sys.taskInit(function()
            while true do
                sys.wait(15*60*1000)
                if not sConnected then
                    if nil == failed_times then
                        failed_times = 0
                    end
                    failed_times = failed_times + 1
                    fskv.set("failed_times", failed_times)
                    sys.publish("REBOOT_SAFE")
                end
                
            end
        end)
        if failed_times == 10 then
            failed_times = 0
            fskv.set("failed_times", failed_times)
            aliyun.setup(tPara,1)
        else
            aliyun.setup(tPara,nil)
        end
        

        sys.taskInit(function()
            while true do
                
                local last_sConnected = sConnected
                sys.wait(1000)
                if sConnected ~= last_sConnected then
                    if sConnected then
                        -- log.info("aliyun.connect","success")
                        sys.publish("LINK_LED",1)
                    else
                        -- log.info("aliyun.connect","failed")
                        sys.publish("LINK_LED",0)
                    end
                    last_sConnected = sConnected
                end
            end
        end)
	   


    end
)



-- 业务看门狗
local softwareDogCo = sys.taskInit(function()
    while true do
        if sys.wait(400000) == nil then --连续400s没有喂狗，触发重启
            log.info("softwareDogCo","feed dog fail")
            sys.publish("REBOOT_SAFE")
        end
    end
end)
    
sys.timerLoopStart(function()
    coroutine.resume(softwareDogCo,"feed")
end, 100000)                        -- 100s喂狗一次



--三分钟上报一次4G信号强度
sys.timerLoopStart(GateWayPostRSSI, 180000)

--一个小时保存一次
sys.timerLoopStart(cal_times_and_store_param, 60*60*1000)

--每小时进行一些时间同步
sys.timerLoopStart(ntp_sync, 3600000)

--todo 
--数据恢复
--定时采集上传