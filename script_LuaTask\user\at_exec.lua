
local sys = require("sys")
local ntc = require("ntc")
local echip = require("hlw8110")
local hlc668 = require("hlc668")
--硬件类型
HardWare = _G.PROJECT
SoftVersion = _G.VERSION

-- local uartid = uart.VUART_0 -- USB虚拟串口的固定id

--[[
@description: 条件返回，入参成立，return 指定字符串，否则返回ERR
@return {*}
--]]
local function at_ret_con( con ,val)
    if con ~= nil then
        if nil == val then
            return "ERR\r\n"
        elseif  "string" ~= type(val) then
            return tostring(val)..'\r\n'
        else
            return val..'\r\n'
        end 
    else
        return "ERR\r\n"
    end
end

-- --设置三元组
-- local function set_pnk_cb(str_recv)
--     local pattern = "=([^,%s]+)%s*,%s*([^,%s]+)%s*,%s*([^,%s]+)%s*$"
--     local p, n, s = string.match(str_recv, pattern)

--     if p and n and s then
--         fskv.set("product_key", p)
--         fskv.set("device_name", n)
--         fskv.set("device_secret", s)
--         return "OK\r\n"
--     else
--         return "ERR\r\n"
--     end
-- end

-- --获取三元组
-- local function get_pnk_cb(str_recv)
--     local p, n, s = fskv.get("product_key"), fskv.get("device_name"), fskv.get("device_secret")
    
--     if p and n and s then
--         return p..','..n..','..s.."\r\n"
--     else
--         return "ERR\r\n"
--     end
-- end

-- --设置端口和地址
-- local function set_hp_cb(str_recv)
--     local pattern = "=%s*([^,%s]+)%s*,%s*([^,%s]+)%s*$"
--     local h, p = string.match(str_recv, pattern)

--     if h and p then
--         fskv.set("mqtt_host", h)
--         fskv.set("mqtt_port", p)      
--         return "OK\r\n"
--     else
--         return "ERR\r\n"
--     end
-- end

-- --获取端口和地址
-- local function get_hp_cb(str_recv)
--     local h,p = fskv.get("mqtt_host"), fskv.get("mqtt_port")
--     if h and p then
--         return h..','..p..'\r\n'
--     else
--         return "ERR\r\n"
--     end
-- end

--获取rsrp和rsrq以及注册状态
local function get_mobile_cb(str_recv)
    local h,p,s = mobile.rsrp(), mobile.rsrq(), mobile.status()
    if h and p and s then
        return tostring(h)..','..tostring(p)..','..tostring(s)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--获取rssi
local function get_csq_cb(str_recv)
    local h = mobile.csq()
    return at_ret_con(h,h)
end


--获取电压
local function get_bat_cb(str_recv)
    adc.open(adc.CH_VBAT)
    local bat = adc.get(adc.CH_VBAT)
    adc.close(adc.CH_VBAT) 
    -- return at_ret_con(bat,tostring(bat/1000)) 
    if bat then
        return tostring(bat/1000)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--查询软件版本
local function get_version_cb(str_recv)
    return HardWare..','..SoftVersion..'\r\n'
end


--[[
@description: 获取当前基站ID
@return {*}
--]]
local function get_mobile_eci(str_recv)
    local h = mobile.eci()
    
    return at_ret_con(h,h)
end

local function get_mobile_iccid()
    local h = mobile.iccid()
    return at_ret_con(h,h)
end
local function get_mobile_imei()
    local h = mobile.imei()
    return at_ret_con(h,h)
end
local function get_mobile_temp()
    local h = ntc.get_cpu_temp()
    return at_ret_con(h,h)
end
local function set_mobile_reboot()
    sys.publish("REBOOT_SAFE")
    return "OK\r\n"
end

local function get_ntc()
    local h = ntc.get_data()
    return at_ret_con(h,h)
end


-- local function get_ntc0()
--     local h = ntc.get_temp_external()
--     return at_ret_con(h,h)
-- end

local function get_echip_ecf()
    local e_data = echip.get_data()
    local h = e_data.cfactor
    return at_ret_con(h,h)
end
local function get_echip_evf()
    local e_data = echip.get_data()
    local h = e_data.vfactor
    return at_ret_con(h,h)
end

local function get_echip_efp()
    local e_data = echip.get_data()
    local h = e_data.factor
    return at_ret_con(h,h)
end

local function get_echip_ec()
    local e_data = echip.get_data()
    local h = e_data.i
    return at_ret_con(h,h)
end
local function get_echip_ev()
    local e_data = echip.get_data()
    local h = e_data.u
    return at_ret_con(h,h)
end
local function get_echip_p()
    local e_data = echip.get_data()
    local h = e_data.p
    return at_ret_con(h,h)
end
local function get_echip_ep()
    local e_data = echip.get_data()
    local h = e_data.ep
    return at_ret_con(h,h)
end




local function get_rf_code(str_recv)
    local h = ac_status.code
    return at_ret_con(h,h)
end

local function get_rf_temp(str_recv)
    local h = ac_status.temp
    return at_ret_con(h,h)
end

local function get_rf_mode(str_recv)
    local h = ac_status.mode
    return at_ret_con(h,h)
end

local function get_rf_fans(str_recv)
    local h = ac_status.fan
    return at_ret_con(h,h)   
end

local function get_rf_fanm(str_recv)
    local h = ac_status.fan_type
    return at_ret_con(h,h)   
end

local function get_ac_crp()
    local h = ac_status.c_run_power
    return at_ret_con(h,h) 
end

local function get_ac_rp()
    local h = ac_status.run_power
    return at_ret_con(h,h) 
end

local function get_ac_st(str_recv)
    local h = ac_status.runningstatus
    return at_ret_con(h,h) 
end

local function get_ac_cst(str_recv)
    local h = ac_status.compress_status
    return at_ret_con(h,h) 
end




--[[
@description: 获取空调开机总时长 单位h
@return {*}
--]]
local function get_ac_rt(str_recv)
    local h = ac_status.using_time
    return at_ret_con(h,h/3600) 
end
--[[
@description: 获取空调压缩机总时长 单位h
@return {*}
--]]
local function get_ac_crt(str_recv)
    local h = ac_status.compressor_time
    return at_ret_con(h,h/3600) 
end

--[[
@description: 获取空调最后的开机时长 单位min
@return {*}
--]]
local function get_ac_rtl(str_recv)
    local h = ac_status.current_running_time
    return at_ret_con(h,h/60) 
end

--[[
@description: 解析字符串为1个数字
@return {成功返回数字，失败返回false}
--]]
local function ret_number(str_recv)
    local pattern = "=%s*([^,%s]+)%s*$"
    local h = string.match(str_recv, pattern)
    log.info("ret_number",h)
    if nil ~= h then
        -- body
        local num = tonumber(h)
        if nil ~= num then
            return num
        else
            return false
        end
    else
        return false
    end
end

local function set_temp_offset(str_recv )
    local num = ret_number(str_recv)
    if num then   
        ac_status.temp_offset = num
        sys.publish("SAVE_ONE_DATA", "temp_offset",ac_status.temp_offset)
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local function set_eep(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_ep(num)     
        ac_status.ep = num
        ac_status.ep_init = num
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

-- local function set_ecf(str_recv)
--     local num = ret_number(str_recv)
--     if num then
--         echip.set_cfactor(num) 
--         ac_status.cfactor = num
--         return "OK\r\n"
--     else
--         return "ERR\r\n"
--     end
-- end
-- local function set_evf(str_recv)
--     local num = ret_number(str_recv)
--     if num then
--         echip.set_vfactor(num) 
--         ac_status.vfactor = num
--         return "OK\r\n" 
--     else
--         return "ERR\r\n"
--     end
-- end

local function set_efp(str_recv)
    local num = ret_number(str_recv)
    if num then
        echip.set_power_factor(num) 
        ac_status.factor = num
        return "OK\r\n" 
    else
        return "ERR\r\n"
    end
end

-- local function set_erst(str_recv)
--     local num = ret_number(str_recv)
--     if num then
--         echip.rst(num)     
--         return "OK\r\n"
--     else
--         return "ERR\r\n"
--     end
    
-- end


local rf_cmd = {
   ["open"] = 1,
   ["openm"] = 2,
   ["mode"] = 3,
   ["temp"] = 4,
   ["fans"] = 5,
   ["fanm"] = 6,
   ["code"] = 7,
   ["match"] = 8,

}


local function set_rf_open(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["open"],num)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_openm(str_recv)

    local pattern = "=(%d+),(%d+)"
    local mode, temp = string.match(str_recv, pattern)
    if mode and temp then
        local ret = hlc668.rf_cmd(rf_cmd["openm"],tonumber(mode),tonumber(temp))     
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_mode(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["mode"],num)  
        if true == ret then
            ac_status.mode = num
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_temp(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["temp"],num)  
        if true == ret then
            ac_status.temp = num
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_fans(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["fans"],num)  
        if true == ret then
            ac_status.fan = num
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_fanm(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["fanm"],num)  
        if true == ret then
            ac_status.fan_type = num
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_code(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["code"],num)  
        if true == ret then
            ac_status.code = num
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_rf_match(str_recv)
    local num = ret_number(str_recv)   
    if num then
        local ret = hlc668.rf_cmd(rf_cmd["match"],1)  
        if true == ret then
            return "OK\r\n"
        else
            return "ERR\r\n"
        end
    else
        return "ERR\r\n"
    end
end

local function set_ac_crp(str_recv)
    local num = ret_number(str_recv)   
    if num then
        set_c_run_power(num)
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local function set_ac_rp(str_recv)
    local num = ret_number(str_recv)   
    if num then
        set_run_power(num)
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local function set_rst(str_recv)
    local num = ret_number(str_recv)   
    if 1 == num then
        sys.publish("RESET_PARAMS")
        sys.waitUntil("RESET_PARAMS_DONE",2000)
        sys.publish("REBOOT_SAFE")
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end



local function get_read_all()
    
    local str = json.encode(ac_status)
    if str then
        return str
    else
        return "ERR\r\n"
    end
    
end



--给出一个能够取消测试方法
local function set_test_status(str_recv)
    local num = ret_number(str_recv)   
    local test_result = fskv.get('test_result')
    if false ~= num then
        test_result['result'] = num
        fskv.set('test_result',test_result) 
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

local user_at_cmd_table = {
    ["+MOB?"] = get_mobile_cb,       -- 获取原始信号强度   获取rsrp和rsrq以及注册状态
    ["+CSQ?"] = get_csq_cb,       -- 获取信号强度  xx,99
    ["+ICCID?"] = get_mobile_iccid,     -- 获取卡号
    ["+IMEI?"] = get_mobile_imei,      -- 获取模组号
    ["+ECI?"] = get_mobile_eci,     --获取当前基站ID
    ["+AIRT?"] = get_mobile_temp,      -- 获取4G模组温度
    ["+AIRV?"] = get_bat_cb,     -- 获取4G模组电源电压  

    -- ["+PNK="] = set_pnk_cb,       -- 设置三元组
    -- ["+PNK?"] = get_pnk_cb,       -- 获取三元组
    -- ["+HP="] = set_hp_cb,        -- 设置MQTT地址和端口
    -- ["+HP?"] = get_hp_cb,        -- 获取MQTT地址和端口
    ["+VER?"] = get_version_cb,       -- 获取软件版本
	-- 					-- 获取硬件版本    
    ["+REBOOT="] = set_mobile_reboot,     -- 重启
    ["+TEST="] = set_test_status,       --设置测试状态，0，不测，其它数字都需要测试

    ["+NTC?"] = get_ntc,       -- 获取内部NTC温度
    ["+TEMPOFFSET="] = set_temp_offset,       -- 获取内部NTC偏移

    -- ["+NTC0?"] = get_ntc0,      -- 获取外部NTC温度NTC0        
    -- ["+ECF="] = set_ecf,  -- 设置电流系数,X为设置的系数    
    -- ["+ECF?"] = get_echip_ecf,   -- 获取电流系数    
    -- ["+EVF="] = set_evf,  -- 设置电压系数,X为设置的系数    
    -- ["+EVF?"] = get_echip_evf,   -- 获取电压系数
    ["+EFP="] = set_efp,  -- 设置电流系数,X为设置的系数    
    ["+EFP?"] = get_echip_efp,   -- 获取电流系数 
    ["+EC?"] = get_echip_ec,        -- 获取电流
    ["+EV?"] = get_echip_ev,        -- 获取电压
    ["+EP?"] = get_echip_p,        -- 获取功率
    ["+EEP?"] = get_echip_ep,       -- 获取电量
    ["+EEP="] = set_eep,      -- 设置电量,X为设置的初始电量值
    -- ["+ERST="] = set_erst,
    -- ["+ERST=1"]      -- 重置计量串口    
    -- ["+ERST=2"]     -- 重置电流、电压系数为1   
    -- ["+ERST=3"]    -- 重置电量  
    
    
    ["+RFCODE?"] = get_rf_code,    -- 获取码库ID    
    ["+RFCODE="] = set_rf_code,    -- 设置码库ID    
    ["+RFOPEN="] = set_rf_open,    -- 发送开机命令  0关机  1开机    
    ["+RFOPENM="] = set_rf_openm,   -- 模式开机命令，2个入参，模式、温度  0自动 1制冷默认26度；2除湿；3送风默认20度；4制热默认20度；    
    ["+RFTEMP?"] = get_rf_temp,    -- 获取空调设定温度    
    ["+RFTEMP="] = set_rf_temp,    -- 设置空调设定温度    
    ["+RFMODE="] = set_rf_mode,    -- 设置空调模式   0-自动，1-制冷，2-除湿，3-送风，4-制热    
    ["+RFMODE?"] = get_rf_mode,    -- 获取空调模式
    ["+RFFANS="] = set_rf_fans,     -- 设置空调风速  0-自动，1-低，2-中，3-高    
    ["+RFFANS?"] = get_rf_fans,    -- 获取空调风速
    ["+RFFANM="] = set_rf_fanm,          --设置扫风模式
    ["+RFFANM?"] = get_rf_fanm,         --获取扫风模式
    ["+RFMATCH="] = set_rf_match,   -- 设置进入匹配模式

    ["+ACRP="] = set_ac_rp,           -- 设置开机运行功率阈值
    ["+ACRP?"] = get_ac_rp,      -- 获取开机运行功率阈值
    ["+ACCRP="] = set_ac_crp,          -- 设置压缩机运行功率阈值
    ["+ACCRP?"] = get_ac_crp,     -- 获取压缩机运行功率阈值
    ["+ACCST?"] = get_ac_cst,     -- 获取压缩机状态
    ["+ACST?"] = get_ac_st,      -- 获取开机状态
    ["+ACCRT?"] = get_ac_crt,     -- 获取压缩机总运行时间
    ["+ACCRTL?"] = get_ac_rtl,      -- 获取压缩机本次运行时长
    ["+ACRT?"] = get_ac_rt,      -- 获取空调总运行时间
    ["+RST="] = set_rst,       -- 1重置设备统计、阈值 
    -- ["+SAVE"] = set_save, --保存参数 ，保存的范围为 空调相关的信息和电量、系数等信息
    ["+RALL="] = get_read_all,   --一次读取ac_status所有参数
}




--处理 发过来的AT 指令
local function app_procmd(str_recv)
    log.info("str_recv------------", str_recv)
    local str_rsp
    -- local str_upper = string.upper(str_recv)
    local prefix = string.match(str_recv, "[aA][tT](%+%u+[%u%d])[%s*]")
    if  nil == prefix then
        prefix = string.match(str_recv, "[aA][tT](%+%u+[%u%d]+[%?=%s*])")
    end
    log.info("app_procmd",str_recv ,prefix)
    
    if prefix ~= nil then
        for k, v in pairs(user_at_cmd_table) do
            if k == prefix then
                str_rsp = v(str_recv)
                return str_rsp
                -- break
            end  
        end
        
    end
    return "ERR\r\n"
    
end




at_exec = {
    app_procmd = app_procmd
}

return at_exec

