require "utils"
require "pm"
require "pins"
require "bit"
--require "nvram"

UART_HLW = 2
hlwQueue = {}
hlw_data = {i=0, u=0, p=0, p_f=0, ep=0, ep_init=0, factor=1}  -- 电流，电压，功率，功率因数，电量，k值，脉冲值，校准因子
V_FACTOR = 1.88
C_FACTOR = 1
FAIL_TIMES = 0
P_ARRAY = {0,0,0,0,0}

local old_reg = 0
local k = 0
local start_flag = 0
local update_times = 0
local last_update_times = 0

--设置校准因子
function set_power_factor(factor)
    log.info("set_power_factor", factor)
    hlw_data.factor = factor
    sys.publish("SAVE_ONE_DATA","factor",factor)
    return true
end

--串口接收字符串转化为表
local function str_to_table(str)
    local rx_table = {}
    for i = 1, #str do
        local _, p = pack.unpack(str, 'b', i)
        rx_table[i] = p
        --print(string.format("%02x ", p))
    end
    --print(table.concat(rx_table, " "))
    return rx_table
end

--串口接收字符串转化为表
local function update_power(power_now)
    for i = 1,4 do
        P_ARRAY[i] = P_ARRAY[i+1]
    end
    P_ARRAY[5] = power_now
    
    local sum_p = 0
    for i = 1,5 do
        sum_p = sum_p + P_ARRAY[i]
    end

    return sum_p*0.2
end

--处理接收串口的数据
function hlw_handle_data()
    local rdata = table.concat(hlwQueue)
    local r_table = str_to_table(rdata)
    -- log.info("hlw_handle_data", "len:", #rdata, table.concat(r_table, ","))
    hlwQueue = {}
    local V,C,P,P_F,PF,PP_REG=0,0,0,0,0,0
    --校验
    if #r_table ~= 24 then
        log.info("hlw_handle_data len err ", #r_table)
        FAIL_TIMES = FAIL_TIMES + 1

        if FAIL_TIMES > 1000 then
            log.info("hlw_handle_data", FAIL_TIMES)
            sys.restart("合力为重启1")
        end
        return
    end   


    local sum_cal = 0
    for i=3,23 do     
        sum_cal = sum_cal + r_table[i]
    end
    sum_cal = math.mod(sum_cal, 256)

    if sum_cal ~= r_table[#rdata] then    
        FAIL_TIMES = FAIL_TIMES + 1
        if FAIL_TIMES > 1000 then
            log.info("hlw_handle_data", FAIL_TIMES)
            sys.restart("合力为重启2")
        end
        log.info("hlw_handle_data check error")
        return
    end

    FAIL_TIMES = 0
    update_times = update_times + 1

    if r_table[1] == 0xAA then     
        log.info("hlw_handle_data state error")
        return
    end   

    --print("state flag:", r_table[1])

    --电压计算
    local VP_REG = r_table[3]*65536 + r_table[4]*256 + r_table[5]
    local V_REG = r_table[6]*65536 + r_table[7]*256 + r_table[8]
    if r_table[1] == 0x55 or ( r_table[1] >= 0xf0 and ( bit.band(r_table[1], 0x08) == 0)) then
        V = (VP_REG / V_REG) * V_FACTOR
        -- V = (VP_REG / V_REG) * 0.9 --分体机测试2的系数为1.08
    end

    --电流计算
    local CP_REG = r_table[9]*65536 + r_table[10]*256 + r_table[11]
    local C_REG = r_table[12]*65536 + r_table[13]*256 + r_table[14]
    if r_table[1] == 0x55 or ( r_table[1] >= 0xf0 and ( bit.band(r_table[1], 0x04) == 0)) then
        C = ((CP_REG * 100) / C_REG) / 100.0
    end

    --功率计算
    PP_REG = r_table[15]*65536 + r_table[16]*256 + r_table[17]
    --print("PP_REG: ", PP_REG)
    local P_REG = r_table[18]*65536 + r_table[19]*256 + r_table[20]
    -- P = (PP_REG / P_REG) * 0.9 --分体机测试2的系数为1.08
    P = (PP_REG / P_REG) * V_FACTOR * hlw_data["factor"]

    -- if r_table[1] == 0x55 or ( r_table[1] >= 0xf0 and ( bit.band(r_table[1], 0x02) == 0)) then
    --    P = (PP_REG / P_REG) * V_FACTOR * hlw_data["factor"]
    -- else
    --     return
    -- end


    --功率因数计算
    if V ~=0 and C~=0 and P~=0 then      
        P_F = P/(V*C)/hlw_data["factor"]
    end


    --电量计算
    if start_flag ~= 0 then
        if bit.band(r_table[21],0x80) ~= old_reg then 
            k = k + 1
        end
    else
        start_flag = 1
    end
    old_reg = bit.band(r_table[21],0x80)

    PF = k*65536 + r_table[22]*256 + r_table[23]
    PF_COUNT = ((100000 * 3600) / (PP_REG * V_FACTOR * C_FACTOR)) * 10000
    E_con = ((PF * 10000) / PF_COUNT) / 10000.0 * hlw_data["factor"]

  

    --更新
    hlw_data["u"] = V
    hlw_data["i"] = C

    if P >= 0 and P < 10000 then
        hlw_data["p"] = update_power(P)
    end
    hlw_data["p_f"] = P_F 

    -- 每次脉冲寄存器反转时增加的电量值
    ep_step = ((65535 * 10000) / PF_COUNT) / 10000.0 * hlw_data["factor"]
    local b = 0
    for n=0,2 do
        if hlw_data["ep"]<=E_con + n*ep_step +  hlw_data["ep_init"] then
            b=n
            break
        end
    end
    
    hlw_data["ep"] = E_con + b*ep_step + hlw_data["ep_init"]
    k=k+b
    hlw_data["k"] = k

    
    -- print("v:", V)
    -- print("i:", C)
    -- print("P:", hlw_data["p"])
    -- print("p_f:", P_F)
    -- print("ep:", E_con)
    -- print("k:", k)
    -- print("p_f connt: ", PF_COUNT)
    
end

-- 合力为守护线程，超过60秒没有新的电量数据，直接重启
function hlw_guard_task()
    while true do
        sys.wait(60000)
        if update_times == last_update_times then
            sys.restart("合力为重启3")
        else
            last_update_times = update_times
        end
    end
end

--初始化
function hlw_8032_init()
    log.info("合力为初始化")
    uart.setup(UART_HLW, 4800, 8, uart.PAR_EVEN, uart.STOP_1, nil, 0)
    uart.on(UART_HLW, "receive", function(uid)
        table.insert(hlwQueue, uart.read(uid, 32))
        sys.publish("UART_HLW")
    end)
    sys.subscribe("UART_HLW", hlw_handle_data)
    sys.taskInit(hlw_guard_task)
end

--上电时恢复参数
function restore_hlw_init_ep(nvram_data)
    hlw_data["ep_init"] = nvram_data['ep']    --恢复上电时的值
    hlw_data["ep"] = hlw_data["ep_init"]
    if nvram_data['factor'] ~= nil then
        hlw_data["factor"] = nvram_data['factor'] --恢复校准因子
    end
    nvm.sett("ac_data","ep_init", hlw_data["ep_init"])
    log.info("restore hlw init ep success")
end

--复位时恢复参数
function restore_hlw_k_e(nvram_data)
    hlw_data["ep_init"] = nvram_data['ep_init']    --恢复上电时的值
    hlw_data["ep"] = nvram_data['ep']
    if nvram_data['k'] ~= nil then
        k = nvram_data['k']
    end
    if nvram_data['factor'] ~= nil then
        hlw_data["factor"] = nvram_data['factor'] --恢复校准因子
    end
    log.info("restore hlw k and e success")
end


--hlw_8032_init()

--sys.taskInit(hlw_read_data_test)


