

local sys = require "sys"


local BSP_UART_TIMEOUT = 1000
local UART_AC = 2
local gmirQueue = {}
local writeBuff = {}
local writeBusy = false
local match_status = false
local hlc_668_busy = 0    --0为不忙   1为忙


local buff_len = 512


g_air_cond_sys_param = {
    ['code'] = 0,
    ['mode'] = 1,
    ['temp'] = 26,
    ['fan'] = 1,
    ['fan_type'] = 0
}   --空调参数


-- 测试用参数
ir_send_receive_flag = 0
ir_control_flag = 0
local test_set_temp = 0



local function ir_to_ac(key)
    if key == nil then
        for k,v in pairs(g_air_cond_sys_param) do
            if (nil ~= ac_status[k]) and (nil ~= g_air_cond_sys_param[k])  then
                ac_status[k] = g_air_cond_sys_param[k]
                sys.publish("SAVE_ONE_DATA", k, ac_status[k])
            end
        end
    else
        if (nil ~= ac_status[key]) and (nil ~= g_air_cond_sys_param[key]) then
            ac_status[key] = g_air_cond_sys_param[key]
            sys.publish("SAVE_ONE_DATA", key, ac_status[key])
        end
    end
end

-- 发送函数
function write(uid, str)
    local i
    writeBuff = {}
    for i = 1, #str, buff_len do
        table.insert(writeBuff, str:sub(i, i + buff_len - 1))
    end
    -- log.info("write", "串口缓冲队列的长度:", writeBusy, #table.concat(writeBuff))
    local wrf = {}
    for i = 1, #table.concat(writeBuff) do
        local _, p = pack.unpack(table.concat(writeBuff), 'b', i)
        wrf[i] = p
    end
    -- print(table.concat(wrf, " "))

    if not writeBusy then
        writeBusy = true
        uart.write(uid, table.remove(writeBuff, 1))
    end
end

--串口接收字符串转化为表
local function str_to_table(str)
    local rx_table = {}
    for i = 1, #str do
        local _, p = pack.unpack(str, 'b', i)
        rx_table[i] = p
    end
    -- print(table.concat(rx_table, " "))
    return rx_table
end

--异或校验，table入参
function bsp_uart_rx_data_check(buf)
    local crc = 0
    for i=1, #buf-1 do
        crc = (crc ~ buf[i])
    end

    if crc == buf[#buf] then 
        return true
    end
    
    return false
end

--下发控制命令响应检验，table入参
function bsp_ir_chip_ac_command_check(buf)
    local ret = bsp_uart_rx_data_check(buf)
    if not ret then
        return false
    end
    
    if buf[1] == 0x06 and buf[2] == 0x89 then
        return true
    end
    
    return false
end


--发送数据,数组为入参
local function bsp_uart_tx_data(buf)
    gmirQueue = {}
    local send_str = ''
    for i=1,#buf do 
        send_str = send_str..pack.pack('b', buf[i])
    end

    write(UART_AC, send_str)
end

--发送单个命令
function bsp_ir_chip_send_command(key_id, key_value)
    local buf = {}
    local result = false
    buf[1]  = 0x86
    buf[2]  = 1
    buf[3]  = key_id
    buf[4]  = key_value
    buf[5]  = 0

    local crc =0
    for i=1, #buf do 
        crc = crc ~ buf[i]
    end

    buf[6] = crc & 0xFF
    -- log.info("bsp_ir_chip_send_command", table.concat(buf, " "))
    
    --这里，等待超时宁可死掉也不能死循环
    local i = 10
    while 1 == hlc_668_busy do
        sys.wait(100)
        i = i-1
        if i == 0 then
            break
        end
    end
    hlc_668_busy = 1
    for j=1, 3 do
        bsp_uart_tx_data(buf)
        local ret, rdata = sys.waitUntil("AC_REV_READY", BSP_UART_TIMEOUT)
        if ret == true and rdata ~= nil then
            local rx_buf = str_to_table(rdata)
            ret = bsp_ir_chip_ac_command_check(rx_buf)
            log.info("single command ret ", ret)
            if ret == true then
                log.info("bsp_ir_chip_send_command ","send success")
                result = true
                break
            else
                log.info("bsp_ir_chip_send_command ","send failed")
                result = false
            end
        end
    end
    hlc_668_busy = 0
    return result
end


--发送通用命令, 高低位拼接
function bsp_ir_chip_send_common_command(kv)
	return bsp_ir_chip_send_command(math.modf(kv/256), math.fmod(kv,256));
end


--[[函数名称: bsp_ir_chip_set_air_cond_power() 
功	  能: 空调电源开关指令
参	  数: on = 1 开；on = 0 关；
返 回 值: 无]]
function bsp_ir_chip_set_air_cond_power(on)
    log.info("bsp_ir_chip_set_air_cond_power ", on)
    return bsp_ir_chip_send_command(0, 1-on)
end

--制冷开机,默认26度
function bsp_ir_chip_set_air_cond_power_on_cool(temp)
    log.info("bsp_ir_chip_set_air_cond_power_on_cool ", temp)

	ret = bsp_ir_chip_send_command(0,0)		--开机
	if ret ~= true then
		return ret
    end

    ret = bsp_ir_chip_send_command(1,1)		--设置为制冷模式
	if ret ~= true then
		return ret
    end

	if temp < 16 then
		temp = 16
    end

	if temp > 32 then
		temp = 32
    end

	ret = bsp_ir_chip_send_command(2,temp-16);	--设置温度
	if ret ~= true then
		return ret
    end

    g_air_cond_sys_param["mode"]   = 1
    g_air_cond_sys_param["temp"]   = temp
    ir_to_ac("mode")
    ir_to_ac("temp")
	return ret
end

--制热开机,默认20度
function bsp_ir_chip_set_air_cond_power_on_heat(temp)
    log.info("bsp_ir_chip_set_air_cond_power_on_cool ", temp)

	ret = bsp_ir_chip_send_command(0,0)		--开机
	if ret ~= true then
		return ret
	end

    ret = bsp_ir_chip_send_command(1,4)		--设置为制热模式
	if ret ~= true then
		return ret
	end

	if temp < 16 then
		temp = 16
    end

	if temp > 32 then
		temp = 32
    end


	ret = bsp_ir_chip_send_command(2,temp-16);	--设置温度
	if ret ~= true then
		return ret
	end

    g_air_cond_sys_param["mode"]   = 4
    g_air_cond_sys_param["temp"]   = temp
    ir_to_ac("mode")
    ir_to_ac("temp")
	return ret
end

--过渡季节开机,默认26度，送风
function bsp_ir_chip_set_air_cond_power_on_fan(temp)
    log.info("bsp_ir_chip_set_air_cond_power_on_cool ", temp)

	ret = bsp_ir_chip_send_command(0,0)		--开机
	if ret ~= true then
        log.info("bsp_ir_chip_set_air_cond_power_on_cool ", " open failed ")
		return ret
	end

    ret = bsp_ir_chip_send_command(1,3)		--设置为送风模式
	if ret ~= true then
        log.info("bsp_ir_chip_set_air_cond_power_on_cool ", " set mode failed ")
		return ret
	end

    g_air_cond_sys_param["mode"]   = 3
    ir_to_ac("mode")
	return ret
end


--过渡季节开机,自动模式
function bsp_ir_chip_set_air_cond_power_on_auto(temp)
    log.info("bsp_ir_chip_set_air_cond_power_on_atuo ", temp)

	ret = bsp_ir_chip_send_command(0,0)		--开机
	if ret ~= true then
		return ret
	end

    ret = bsp_ir_chip_send_command(1,0)		--设置为自动模式
	if ret ~= true then
		return ret
	end

    g_air_cond_sys_param["mode"]   = 3
    ir_to_ac("mode")
	return ret
end

--除湿模式
function bsp_ir_chip_set_air_cond_power_on_humi(temp)
    log.info("bsp_ir_chip_set_air_cond_power_on_humi ", temp)

	ret = bsp_ir_chip_send_command(0,0)		--开机
	if ret ~= true then
		return ret
	end

    ret = bsp_ir_chip_send_command(1,2)		--设置为除湿模式
	if ret ~= true then
		return ret
	end

    g_air_cond_sys_param["mode"]   = 2
    ir_to_ac("mode")

	return ret
end

--[[
函数名称: bsp_ir_chip_set_air_cond_mode() 
功	  能: 空调模式设置指令
参	  数: mode:0-自动，1-制冷，2-除湿，3-送风，4-制热
返 回 值: 无
]]
function bsp_ir_chip_set_air_cond_mode(mode)
    log.info("bsp_ir_chip_set_air_cond_mode ", mode)
    if mode ~= 0 and mode ~= 1 and mode ~= 2 and mode ~= 3 and mode ~= 4 then   
        return false   
    end

    local ret = bsp_ir_chip_send_command(1, mode)
    if ret then     -- 更新系统数据
	    g_air_cond_sys_param.mode = mode
        ir_to_ac("mode")
    end
    return ret
end

--[[函数名称: bsp_ir_chip_set_air_cond_fan() 
功	  能: 空调风速设置指令
参	  数: fan:0-自动，1-1级，2-2级，3-3级
返 回 值: 无]]
function bsp_ir_chip_set_air_cond_fan(fan)
    log.info("bsp_ir_chip_set_air_cond_fan ", fan)

    local ret = bsp_ir_chip_send_command(4, fan)

    if ret then     -- 更新系统数据
        g_air_cond_sys_param.fan = fan
        ir_to_ac("fan")
    end

	return ret
end

--[[
函数名称: bsp_ir_chip_set_air_cond_fan_type() 
功	  能: 扫风开关
参	  数: type: 1-关闭扫风，0-打开扫风
返 回 值: 无
]]
function bsp_ir_chip_set_air_cond_fan_type(fan_type)
    if fan_type ~= 0 and fan_type ~= 1 then   
        return false   
    end

    local ret = bsp_ir_chip_send_command(5, fan_type)

    if ret then     -- 更新系统数据
        g_air_cond_sys_param.fan_type = fan_type
        ir_to_ac("fan_type")
    end
    return ret
end

--[[函数名称: bsp_ir_chip_set_air_cond_temp() 
功	  能: 空调温度设置指令
参	  数: 
返 回 值: 无
备	  注: 该指令暂时有异常]]
function bsp_ir_chip_set_air_cond_temp(tmp)
    log.info("bsp_ir_chip_set_air_cond_temp ", tmp)
    local hw_tmp = 26
    --温度需要控制在16~31:0-E
    if tmp <= 32 and tmp >= 16 then    
        hw_tmp = tmp
    end  

    local ret = bsp_ir_chip_send_command(2,hw_tmp-16)

    if ret then   
        g_air_cond_sys_param.temp = hw_tmp
        ir_to_ac("temp")
    end

    return ret
end

function bsp_ir_chip_enable_feedback(enable)
    local buf = {}
    buf[1]  = 0x82
    buf[2]  = enable
    buf[3]  = 0
    buf[4]  = 0
    buf[5]  = 0

    local crc = 0
    for i=1, #buf do 
        crc = crc ~ buf[i]
    end

    buf[6] = crc & 0xFF

    bsp_uart_tx_data(buf)

    local ret, rdata = sys.waitUntil("AC_REV_READY", BSP_UART_TIMEOUT)
    if ret ~= true or rdata == nil then
        return false
    end

    local rx_buf = str_to_table(rdata)
    ret = bsp_ir_chip_ac_command_check(rx_buf)
    -- log.info("bsp_ir_chip_enable_feedback ret ", ret)
    return ret
end




function bsp_ir_get_chip_version()
    local buf = {}
    buf[1]  = 0x92
    buf[2]  = 0
    buf[3]  = 0
    buf[4]  = 0
    buf[5]  = 0

    local crc =0
    for i=1, #buf do 
        crc = crc ~ buf[i]
    end

    buf[6] = crc & 0xFF

    bsp_uart_tx_data(buf)

    local ret, rdata = sys.waitUntil("AC_REV_READY", BSP_UART_TIMEOUT)
    if ret ~= true or rdata == nil then
        return false
    end

    local rx_buf = str_to_table(rdata)
    ret = bsp_ir_chip_ac_command_check(rx_buf)
    if true == ret then
        g_air_cond_sys_param["version"] = rx_buf[3]
    end
    log.info("bsp_ir_get_chip_version ret ", ret, g_air_cond_sys_param["version"])
    return ret
end

--创建遥控器
function creat_control(kfid)
    local buf = {}

    buf[1]  = 0x80
    buf[2]  = 0x01
    buf[3]  = math.modf(kfid/256)
    buf[4]  = math.fmod(kfid,256)
    buf[5]  = 0x00
 
    local crc =0
    for i=1, #buf do 
        crc = crc ~ buf[i]
    end

    buf[6] = crc & 0xFF

    
    bsp_uart_tx_data(buf)


    local ret, rdata = sys.waitUntil("AC_REV_READY", 1100)
    if ret ~= true or rdata == nil then
        log.info("creat_control","timeout")
        return false
    end

    local rx_buf = str_to_table(rdata)
    if #rx_buf ~= 6 then
        log.info("creat_control","rxbuf length error")
        return false
    end
    ret = bsp_uart_rx_data_check(rx_buf)
    if not ret then
        log.info("creat_control","check error")
        return false
    end

    log.info("creat_control success code ", kfid)
    
    bsp_ir_chip_enable_feedback(1)

    return true
end



--[[
函数名称: bsp_ir_chip_one_step_len() 
功	  能: 一键匹配
参	  数: on: 0-关闭，1-显示
返 回 值: 无]]
function bsp_ir_chip_one_step_match(on)
    if on ~= 1 then
        return true
    end

    local buf = {}

    buf[1]  = 0x81
    buf[2]  = 0x01
    buf[3]  = 0x00
    buf[4]  = 0x00
    buf[5]  = 0x00
 
    local crc =0
    for i=1, #buf do 
        crc = crc ~ buf[i]
    end

    buf[6] = crc & 0xFF

    bsp_uart_tx_data(buf)

    local ret, rdata = sys.waitUntil("AC_REV_READY", 11000)
    if ret ~= true or rdata == nil then
        log.info("bsp_ir_chip_one_step_match ", "timeout")
        return false
    end

    local rx_buf = str_to_table(rdata)
    if #rx_buf ~= 6 then
        log.info("bsp_ir_chip_one_step_match ", "rx len error", #rx_buf)
        return false
    end
    ret = bsp_uart_rx_data_check(rx_buf)
    if not ret then
        log.info("bsp_ir_chip_one_step_match ", "rx check error")
        return false
    end
    
    g_air_cond_sys_param.code = rx_buf[3]*256 + rx_buf[4]

    log.info("bsp_ir_chip_one_step_match success code ", g_air_cond_sys_param.code)
    
    creat_control(g_air_cond_sys_param.code)

    match_status = true
    ir_to_ac("code")
    sys.publish("SAVE_ONE_DATA","code",g_air_cond_sys_param.code)
    return true
end

--设置码库
function bsp_ir_chip_set_code(code)
    if code == -1 then    
        return match_status
    end

    if code < -1 or code > 1000 then
        return false
    end

    ret = creat_control(code)
    if not ret then
        return false
    end
    g_air_cond_sys_param.code = code
    ir_to_ac("code")
    sys.publish("SAVE_ONE_DATA","code",code)
    return true
end

--获取用户设置的状态
function get_user_control_status(rdata)
    print("in get_user_control_status ")
    log.info("get_user_control_status", "rx len in")
    if rdata == nil then 
        return
    end  

    local rx_buf = str_to_table(rdata)
    if #rx_buf ~= 6 or (rx_buf[1] ~= 0x08 and rx_buf[1] ~= 0x06 )then
        log.info("get_user_control_status", "rx len error", #rx_buf, rx_buf[1],type(rx_buf[1]))
        return
    end

    ret = bsp_uart_rx_data_check(rx_buf)
    if ret == true  then  
        if rx_buf[2] > 2 or rx_buf[3] > 4 or rx_buf[4] > 14 or rx_buf[5] > 3 then
            return
        end

        --组合校验，认为两个元素不能同时变化
        if (g_air_cond_sys_param.mode ~= rx_buf[3] and g_air_cond_sys_param.temp ~= rx_buf[4] + 16)
            or (g_air_cond_sys_param.fan ~= rx_buf[5] and  g_air_cond_sys_param.mode ~= rx_buf[3])
            or (g_air_cond_sys_param.temp ~= rx_buf[4] + 16 and g_air_cond_sys_param.fan ~= rx_buf[5]) then
            return
        end

        g_air_cond_sys_param.mode = rx_buf[3]
        g_air_cond_sys_param.temp = rx_buf[4] + 16
        g_air_cond_sys_param.fan  = rx_buf[5]
        ir_to_ac(nil)
        log.info("get_user_control_status", rx_buf[3], rx_buf[4], rx_buf[5])
        sys.publish("FAST_UP", 1)       --快速上报一次

    else
        log.info("get_user_control_status", "rx check error")
    end
    
end


--参数初始化，创建遥控器
function bsp_ir_chip_air_cond_init()
    -- pm.wake("mcuart")
    -- uart.setup(UART_AC, 9600, 8, uart.PAR_NONE, uart.STOP_1,nil, 1)
    uart.setup(UART_AC, 9600, 8, 1, uart.NONE)
    uart.on(UART_AC, "receive", function(uid)
        log.info("on receive")
        table.insert(gmirQueue, uart.read(uid, buff_len))
        local rdata = table.concat(gmirQueue)
        
        sys.publish("AC_REV_READY", rdata)
        gmirQueue = {}
    end)
    sys.subscribe("AC_REV_READY", get_user_control_status)
    -- 
    uart.on(UART_AC, "sent", function()
        if #writeBuff == 0 then
            writeBusy = false
            sys.publish("done")
            -- log.info("uart ac send done")
        else
            writeBusy = true
            uart.write(UART_AC, table.remove(writeBuff, 1))
            -- log.info("bsp_ir_chip_air_cond_init", "uart ac send ing...")
        end
    end)

    creat_control(g_air_cond_sys_param['code'])
    log.info("bsp_ir_chip_air_cond_init","code", g_air_cond_sys_param['code'])
end





--获取用户设置的状态
local function get_user_control_status_test(rdata)
    log.info("get_user_control_status_test ", rdata)
    if rdata == nil then 
        return
    end  

    local rx_buf = str_to_table(rdata)
    log.info("get_user_control_status_test", "rx_buf", rx_buf[1], rx_buf[2], rx_buf[3], rx_buf[4], rx_buf[5], rx_buf[6])
    if #rx_buf ~= 6 or (rx_buf[1] ~= 0x08 and rx_buf[1] ~= 0x06 ) then
        log.info("get_user_control_status_test ", "rx_buf len error", #rx_buf)
        return
    end
    
    local ret = bsp_uart_rx_data_check(rx_buf)
    if ret == true  then  
        if rx_buf[2] > 2 or rx_buf[3] > 4 or rx_buf[4] > 14 or rx_buf[5] > 3 then
            log.info("get_user_control_status_test", "data error ", rx_buf[2], rx_buf[3])
        else
            local temp = rx_buf[4] + 16
            log.warn("收到红外指令：", temp)
            if temp == test_set_temp + 1 then
                log.warn("红外遥控测试完成")
                ir_send_receive_flag = 1
            end
        end
    else
        log.info("get_user_control_status_test", "rx check error")
    end
    
end

-- 得到一个真实的随机数
function GetTrueRandom(min,max)
    --得到时间字符串
    local strTime=tostring(os.time())
    --得到一个反转字符串
    local strRev=string.reverse(strTime)
    --得到前6位
    local strRandomTime=string.sub(strRev, 1, 6)
 
    --设置时间种子
    math.randomseed(strRandomTime)
    --输出随机数
    --print("#随机数=",math.random(min, max))
    return math.random(min, max)
end

local function ir_send_receive_test()
    local k=3
    local time=1000
    while k>0 do
        k = k-1
        test_set_temp = GetTrueRandom(16,28)
        bsp_ir_chip_set_air_cond_temp(test_set_temp)
        log.warn("发送设置温度：", test_set_temp)
        sys.wait(time)
        if 1==ir_send_receive_flag then
            log.info("红外测试通过", test_set_temp)
            return 
        end
    end   
end



--参数初始化，创建遥控器，测试完成后需要恢复原有的遥控器
function bsp_ir_chip_air_cond_test_init()
    log.info("bsp_ir_chip_air_cond_test_init","uart init")
    -- uart.setup(UART_AC, 9600, 8, uart.PAR_NONE, uart.STOP_1,nil, 1)
    -- uart.setup(UART_AC, 9600, 8, 1, uart.NONE)
    
    -- uart.on(UART_AC, "receive", function(uid)
    --     log.info("on receive")
    --     table.insert(gmirQueue, uart.read(uid, 8192))
    --     local rdata = table.concat(gmirQueue)
        
    --     sys.publish("AC_REV_READY_TEST", rdata)
    --     gmirQueue = {}
    -- end)
    -- 测试完成后需要取消注册
    local ret = creat_control(120)
    sys.subscribe("AC_REV_READY", get_user_control_status_test)
    -- 
    log.info("bsp_ir_chip_air_cond_test_init","start creat_control")
    
    -- ret = creat_control(40)
    if not ret then  
        log.warn("创建遥控器失败")
        ir_control_flag = 0
        return
    else    
        log.warn("创建遥控器成功")
        ir_control_flag = 1
    end
    log.info("bsp_ir_chip_air_cond_test_init","start ir_send_receive_test")
    ir_send_receive_test()    
    sys.unsubscribe("AC_REV_READY", get_user_control_status_test)

    -- set_temp = GetTrueRandom(16,28)
    -- bsp_ir_chip_set_air_cond_temp(set_temp)
    -- log.warn("发送设置温度：", set_temp)

    creat_control(g_air_cond_sys_param['code'])
    log.info("get_user_control_status_test finished ", "code", g_air_cond_sys_param['code'])
end



function test()
    -- bsp_ir_chip_air_cond_test_init()
    local ac_status = {
        ['power_status'] = 0,
        ['ac_status'] = 0,
        ['compress_status'] = 0,
        ["mode"]       = 1,
        ["temp"]       = 26,
        ["fan"]        = 0,
        ["fan_type"]   = 1,
        ["temp_indoor"] = 0,
        ["humi"]       = 0,
        ['code']       = 0,
        ['i']          = 0,
        ['u']          = 0,
        ['p']          = 0,
        ['p_f']        = 0,
        ['ep']         = 0,
        ['compressor_time'] = 0,
        ['current_running_time'] = 0,
        ['using_time'] = 0,
        ['run_power'] = 8.5,
        ['c_run_power'] = 60
    } 
    sys.taskInit( function()
        while true do
            bsp_ir_chip_air_cond_init(ac_status)
            log.warn("test "," 发送开机命令")
            bsp_ir_chip_set_air_cond_power(1)
            sys.wait(2000)
            local set_temp = GetTrueRandom(16,28)
            log.warn("test "," 发送设置温度：", set_temp)
            bsp_ir_chip_set_air_cond_temp(set_temp)
            sys.wait(2000)
            log.warn("test "," 发送关机命令")
            bsp_ir_chip_set_air_cond_power(0)
            sys.wait(10000)
        end
    end)
    
    -- bsp_ir_chip_one_step_match(1)
end


local function init()
    bsp_ir_chip_air_cond_init()
end

local function get_data()
    return g_air_cond_sys_param
end

local function recover_data(data)
    for k,v in pairs(g_air_cond_sys_param) do
        if nil ~= data[k] then
            g_air_cond_sys_param[k] = data[k]
        end
        log.info("668 recover_data", k, g_air_cond_sys_param[k])
    end
    
end

local function cmd_open(num)
    local ret = false
    if 0 ==  num then
        ret = bsp_ir_chip_set_air_cond_power(0)
    elseif 1 == num then
        ret = bsp_ir_chip_set_air_cond_power(1)
    else
        ret = false
    end
    return ret
end

local function cmd_open_mode(mode,temp)
    local ret = false
    if mode and temp then
        if 0 == mode then
            ret = bsp_ir_chip_set_air_cond_power_on_auto(temp)
        elseif 1 == mode then
            ret = bsp_ir_chip_set_air_cond_power_on_cool(temp)
        elseif 2 == mode then
            ret = bsp_ir_chip_set_air_cond_power_on_humi(temp)
        elseif 3 == mode then
            ret = bsp_ir_chip_set_air_cond_power_on_fan(temp)
        elseif 4 == mode then
            ret = bsp_ir_chip_set_air_cond_power_on_heat(temp)
        else
            ret = false
        end
        return ret
    else
        return false
    end
   
end


local function cmd_set_mode(mode)
    local ret = bsp_ir_chip_set_air_cond_mode(mode)
    return ret
end

local function cmd_set_temp(temp)
    local ret = bsp_ir_chip_set_air_cond_temp(temp)
    return ret
end

local function cmd_set_fanspeed(fan)
    local ret = bsp_ir_chip_set_air_cond_fan(fan)
    return ret
end

local function cmd_set_fantype(fantype)
    local ret = bsp_ir_chip_set_air_cond_fan_type(fantype)
    return ret
end

local function cmd_set_code(code)
    local ret = bsp_ir_chip_set_code(code)
    return ret
end
local function cmd_set_match(num)
    local ret = bsp_ir_chip_one_step_match(1)
    return ret
end

--[[
@description: 注册这个函数方便解耦，用于处理命令
@return {*}
--]]
local hlc668_cmd = {
    [1] = cmd_open, --开机
    [2] = cmd_open_mode, --模式开机
    [3] = cmd_set_mode,
    [4] = cmd_set_temp,
    [5] = cmd_set_fanspeed,
    [6] = cmd_set_fantype,
    [7] = cmd_set_code,
    [8] = cmd_set_match
}
-- 必须在在任务中使用，否则容易出现问题
local function rf_command(cmd,val,val1)
    log.info("rf_command ",cmd,val,val1)
    local len = #hlc668_cmd
    if cmd > len then
        return false
    end
    if 2 == cmd then
        return hlc668_cmd[cmd](val,val1)
    else
        return hlc668_cmd[cmd](val)
    end
end

local hlc668 = {
    init = init,
    test = test,
    get_data = get_data,
    recover_data = recover_data,
    rf_cmd = rf_command,
}

return hlc668