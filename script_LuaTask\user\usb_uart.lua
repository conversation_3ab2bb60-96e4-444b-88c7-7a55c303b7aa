
local sys = require("sys")


--硬件类型
HardWare = "SC-A-G01"
SoftVersion = "1.0.0"

local uartid = uart.VUART_0 -- USB虚拟串口的固定id

--设置三元组
local function set_pnk_cb(str_recv)
    local pattern = "=([^,%s]+)%s*,%s*([^,%s]+)%s*,%s*([^,%s]+)%s*$"
    local p, n, s = string.match(str_recv, pattern)

    if p and n and s then
        fskv.set("product_key", p)
        fskv.set("device_name", n)
        fskv.set("device_secret", s)
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

--获取三元组
local function get_pnk_cb(str_recv)
    local p, n, s = fskv.get("product_key"), fskv.get("device_name"), fskv.get("device_secret")
    if p and n and s then
        return p..','..n..','..s.."\r\n"
    else
        return "ERR\r\n"
    end
end

--设置端口和地址
local function set_hp_cb(str_recv)
    local pattern = "=%s*([^,%s]+)%s*,%s*([^,%s]+)%s*$"
    local h, p = string.match(str_recv, pattern)

    if h and p then
        fskv.set("mqtt_host", h)
        fskv.set("mqtt_port", p)      
        return "OK\r\n"
    else
        return "ERR\r\n"
    end
end

--获取端口和地址
local function get_hp_cb(str_recv)
    local h,p = fskv.get("mqtt_host"), fskv.get("mqtt_port")
    if h and p then
        return h..','..p..'\r\n'
    else
        return "ERR\r\n"
    end
end

--获取rsrp和rsrq以及注册状态
local function get_mobile_cb(str_recv)
    local h,p,s = mobile.rsrp(), mobile.rsrq(), mobile.status()
    if h and p and s then
        return tostring(h)..','..tostring(p)..','..tostring(s)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--获取rssi
local function get_csq_cb(str_recv)
    local h = mobile.rssi()
    if h then
        return tostring(h)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--获取温湿度值
local function get_ht_cb(str_recv)
    local h,p = sht30_read()
    if h and p then
        return tostring(h)..','..tostring(p)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--获取温湿度值
local function get_ct_cb(str_recv)
    local h,p = sgp30_read()
    if h and p then
        return tostring(h)..','..tostring(p)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--获取电压
local function get_bat_cb(str_recv)
    adc.open(adc.CH_VBAT)
    local bat = adc.get(adc.CH_VBAT)
    adc.close(adc.CH_VBAT)    
    if bat then
        return tostring(bat/1000)..'\r\n'
    else
        return "ERR\r\n"
    end
end

--查询软件版本
local function get_version_cb(str_recv)
    return HardWare..','..SoftVersion..'\r\n'
end

local user_at_cmd_table = {
    ["+PNK"] = set_pnk_cb,     --设置三元组
    ["+PNK?"] = get_pnk_cb,    --获取三元组
    ["+HP"] = set_hp_cb,       --设置MQTT地址和端口
    ["+HP?"] = get_hp_cb,      --获取MQTT地址和端口
    ["+MOB?"] = get_mobile_cb,    --查询信号强度
    ["+BAT?"] = get_bat_cb,       --查询电池电压
    ["+HT?"] = get_ht_cb,         --查询温湿度
    ["+CT?"] = get_ct_cb,         --查询温湿度
    ["+CSQ?"] = get_csq_cb,         --查询信号强度
    ["+VER?"] = get_version_cb    --查询软件版本
}

--处理PC 发过来的AT 指令
local function app_procmd(str_recv)
    log.info("str_recv------------", str_recv)

    local prefix = string.match(str_recv, "[aA][tT](%+%u+[%u%?]+)")
    if prefix ~=nil then
        for k, v in pairs(user_at_cmd_table) do
            if k == prefix then
                str_rsp = v(str_recv)
                break
            end  
        end
    end

    return str_rsp
end

-- usb_at处理入口
local function usb_at_proc(str_recv)
    local str_rsp = app_procmd(str_recv)
    if str_rsp ~=nil then
        uart.write(uartid, str_rsp)
    end
end

uart.setup( uartid,115200,8,1)
-- 收取数据会触发回调, 这里的"receive" 是固定值
uart.on(uartid, "receive", function(id, len)
    local s = ""
    repeat
        s = uart.read(id, len)
        if s and #s > 0 then -- #s 是取字符串的长度
            log.info("uart", "receive", id, #s, s)
            sys.publish("at", s)
        end
    until s == ""
end)

uart.on(uartid, "sent", function(id)
    log.info("uart", "sent", id)
end)

sys.subscribe("at", usb_at_proc)