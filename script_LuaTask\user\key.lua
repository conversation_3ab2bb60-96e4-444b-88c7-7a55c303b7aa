
local sys = require "sys"
local hlc668 = require "hlc668"
-- 按键功能定义
-- 1、一键匹配     -短按2次
-- 2、复位         -长按
-- 3、测试         -短按3次

-- 可以实现的动作
-- 1、长按
-- 2、短按1次
-- 3、短按2次
-- 4、短按3次
-- 5、按住上电，松开
local USER_PIN = 29
local KEY_UP = 1
local KEY_DOWN = 0
local key_press_count = 0

local long_press_threshold = 8000  -- 长按阈值，单位毫秒
local short_press_interval = 1000
local keytime = 0
local key_time_run_poll = 10
local press_time = 0
local release_time = 0

local LED_ENABLE = false
local LED_STA_GPIO = 27
local gledRun = 0

local function len_enable()
    if false == LED_ENABLE then
        LED_ENABLE = true
    else
        LED_ENABLE = false
    end
    
end

--登录成功闪烁
local function Ledtask()
	while true do
		if gledRun == 0 and true == LED_ENABLE then 
			gpio.setup(LED_STA_GPIO,1)
			gledRun = 1
        elseif gledRun == 1 and true == LED_ENABLE then
			gpio.setup(LED_STA_GPIO,0)
			gledRun = 0
        else
            gpio.setup(LED_STA_GPIO,0)
            gledRun = 0
		end
		sys.wait(1000)
	end
end


local key_func = {
    function ()
        sys.publish("REBOOT_SAFE")
    end,
    len_enable,
    bsp_ir_chip_one_step_match,
    bsp_ir_get_chip_version,
    function ()
        -- sys.publish("AC_TEST_START")
        
    end
}



local function key_call_back(long_times,short_times)
    if 1 == long_times then
        log.info("key_call_back ","long press")
        sys.taskInit(key_func[1])
    elseif 1 == short_times then
        log.info("key_call_back ","short press 1")
        sys.taskInit(key_func[2])
    elseif 2 == short_times then
        log.info("key_call_back ", "short press 2")
        sys.taskInit(key_func[3],1)
    elseif 3 == short_times then
        log.info("key_call_back ", "short press 3")
        sys.taskInit(key_func[5])
    end
    press_time = 0
    release_time = 0
    key_press_count = 0
end

-- sys.subscribe("KEY_EVENT",key_call_back)



local function key_time_run()
    keytime = keytime + 1
    if release_time >= press_time then
        if  key_press_count ~= 0  and  keytime - release_time > short_press_interval/key_time_run_poll then
            log.info("key_time_run","key_press_count",key_press_count)
            -- sys.publish("KEY_EVENT",0,key_press_count)
            key_call_back(0,key_press_count)
        end
    else 
        if key_press_count ~= 0  and  keytime - press_time > short_press_interval/key_time_run_poll then
            log.info("key_time_run","key_press_count",key_press_count)
            -- sys.publish("KEY_EVENT",0,key_press_count)
            key_call_back(0,key_press_count)
        end
    end
end


local function key_irq()
    -- log.info("key irq")
    local key_level = gpio.get(USER_PIN)
    if key_level == KEY_UP then
        log.info("key up")
        release_time = keytime
        log.info("release_time",release_time,press_time)
        if release_time - press_time > long_press_threshold/key_time_run_poll and key_press_count == 0 then
            -- sys.publish("KEY_EVENT",1,0)
            key_call_back(1,0)
        elseif release_time - press_time <= short_press_interval/key_time_run_poll then
            key_press_count = key_press_count + 1
        elseif release_time - press_time > short_press_interval/key_time_run_poll
            and release_time - press_time <= long_press_threshold/key_time_run_poll  then
            key_press_count = key_press_count + 1
            -- sys.publish("KEY_EVENT",0,key_press_count)
            key_call_back(0,key_press_count)
        end
    elseif key_level == KEY_DOWN then
        log.info("key down")
        press_time = keytime 
        log.info("press_time",press_time)     
    end 
end

local user_level = KEY_UP
local user_level_last = KEY_UP
local function detect_edge(fun)
    -- 上升沿触发
    if user_level == KEY_UP and user_level_last == KEY_DOWN then
        log.info("key irq up")
        fun()

    -- 下降沿触发
    elseif user_level == KEY_DOWN and user_level_last == KEY_UP then
        log.info("key irq down")
        fun()
    end
    user_level_last = user_level
    user_level = gpio.get(USER_PIN)
end

local function init()
    gpio.setup(USER_PIN,
                nil,
                gpio.PULLUP,
                gpio.BOTH)  --设置按键引脚
    gpio.debounce(USER_PIN, 1, 1)
    user_level = gpio.get(USER_PIN)
    user_level_last = user_level
    sys.taskInit(function ()
        while true do
            detect_edge(key_irq)
            key_time_run()
            sys.wait(key_time_run_poll)
        end
    end)
    -- sys.taskInit(function ()
    --     while true do
    --         sys.wait(key_time_run_poll)
    --     end
    -- end)
end 





local key = {
    init = init,
    Ledtask = Ledtask,
}

return key
