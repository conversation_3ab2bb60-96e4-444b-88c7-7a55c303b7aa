# 合宙平台升级
 ## 4G分体机控制器 PRODUCT_KEY 
 PRODUCT_KEY = "KVRwDQvoGAwBjP7RSFdWmZNH3V5sVXwo"

# 升级包解释
 .binpkg是量产固件  
 .soc是luatos固件包  
 .ota不用管 
 .bin是lua脚本文件 
 .sota是升级包  

1. 量产固件.binpkg  用于初始固件烧写
2. luatos固件包.soc 用于制作含固件的差分升级包
3. lua脚本文件.bin 仅包含lua脚本文件，可用于制作脚本的全量升级包


# 升级包制作
## 全量升级包
### 只升级脚本
直接使用bin文件即可
### 升级固件+脚本  请使用差分升级包

使用soc文件制作差分升级包,使用luatool_V2自带的差分包生成工具
soc文件在生成的时候要勾选 __升级文件包含core__
生成出来的有2个文件 一个是bin文件 一个是sota文件，如果没有bin文件，请使用新的luatool工具
直接使用这个bin文件即可
